<!DOCTYPE html>
<html>
<head>
    <title>Debug ISIC Data</title>
</head>
<body>
    <h1>ISIC Rev.5 Data Debug</h1>
    <div id="debug"></div>
    
    <script src="isic_rev5_data_corrected.js"></script>
    <script>
        console.log('Total items:', isicData.length);
        
        // Find C related entries
        const cEntries = isicData.filter(item => 
            item.code === 'C' || item.code === 'C10' || item.code === 'C11'
        );
        
        let html = '<h2>C Section Related Entries:</h2>';
        cEntries.forEach(item => {
            html += `<p><strong>${item.code}</strong> - Level: "${item.level}" - Parent: "${item.parentCode}" - ${item.name}</p>`;
        });
        
        // Check all sections
        const sections = isicData.filter(item => item.level === 'Section');
        html += '<h2>All Sections:</h2>';
        sections.forEach(item => {
            html += `<p><strong>${item.code}</strong> - ${item.name}</p>`;
        });
        
        // Check C divisions
        const cDivisions = isicData.filter(item => 
            item.level === 'Division' && item.code.startsWith('C')
        );
        html += '<h2>C Divisions:</h2>';
        cDivisions.slice(0, 5).forEach(item => {
            html += `<p><strong>${item.code}</strong> - Parent: "${item.parentCode}" - ${item.name}</p>`;
        });
        
        document.getElementById('debug').innerHTML = html;
    </script>
</body>
</html>