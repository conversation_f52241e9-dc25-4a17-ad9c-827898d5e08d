#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GB/T 4754-2017 CSV文件编码错误检查工具
检查分类层级结构是否符合标准
"""

import csv
import re
from collections import defaultdict

def analyze_code_structure(code):
    """分析代码结构，返回代码类型和长度"""
    if not code or code == '':
        return 'empty', 0
    
    # 门类：字母
    if re.match(r'^[A-Z]$', code):
        return 'category', 1
    
    # 大类：1-2位数字
    if re.match(r'^\d{1,2}$', code):
        length = len(code)
        if length == 1:
            return 'major_1digit', 1
        else:
            return 'major_2digit', 2
    
    # 中类：3位数字
    if re.match(r'^\d{3}$', code):
        return 'medium', 3
    
    # 小类：4位数字
    if re.match(r'^\d{4}$', code):
        return 'subclass', 4
    
    return 'unknown', len(code)

def check_csv_encoding_errors(csv_file_path):
    """检查CSV文件中的编码错误"""
    
    errors = []
    warnings = []
    statistics = defaultdict(int)
    level_mapping = {}
    
    print("正在检查GB/T 4754-2017 CSV文件的编码错误...")
    print("=" * 60)
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            # 跳过BOM
            content = csvfile.read()
            if content.startswith('\ufeff'):
                content = content[1:]
            
            reader = csv.DictReader(content.splitlines())
            
            for row_num, row in enumerate(reader, start=2):  # 从第2行开始（第1行是标题）
                level_zh = row['分类级别']
                level_en = row['Category_level']
                code = row['分类代码']
                parent_code = row['上级代码']
                name_zh = row['分类名称']
                
                # 分析代码结构
                code_type, code_length = analyze_code_structure(code)
                parent_type, parent_length = analyze_code_structure(parent_code)
                
                # 统计
                statistics[f"{level_zh}({level_en})"] += 1
                statistics[f"代码类型_{code_type}"] += 1
                
                # 建立级别映射
                level_mapping[code] = {
                    'level_zh': level_zh,
                    'level_en': level_en,
                    'name_zh': name_zh,
                    'parent_code': parent_code,
                    'code_type': code_type,
                    'code_length': code_length,
                    'row_num': row_num
                }
                
                # 检查1：级别与代码长度的一致性
                expected_code_type = None
                if level_zh == '门类':
                    expected_code_type = 'category'
                elif level_zh == '大类':
                    expected_code_type = ['major_1digit', 'major_2digit']
                elif level_zh == '中类':
                    expected_code_type = 'medium'
                elif level_zh == '小类':
                    expected_code_type = 'subclass'
                
                if expected_code_type:
                    if isinstance(expected_code_type, list):
                        if code_type not in expected_code_type:
                            errors.append({
                                'type': '级别与代码不匹配',
                                'row': row_num,
                                'code': code,
                                'level': level_zh,
                                'issue': f"级别为'{level_zh}'但代码'{code}'不符合预期格式",
                                'expected': f"应为{expected_code_type}之一",
                                'actual': code_type
                            })
                    else:
                        if code_type != expected_code_type:
                            errors.append({
                                'type': '级别与代码不匹配',
                                'row': row_num,
                                'code': code,
                                'level': level_zh,
                                'issue': f"级别为'{level_zh}'但代码'{code}'不符合预期格式",
                                'expected': expected_code_type,
                                'actual': code_type
                            })
                
                # 检查2：parentCode的层级关系
                if parent_code and parent_code != '':
                    # 小类应该指向中类
                    if level_zh == '小类' and code_type == 'subclass':
                        if parent_type != 'medium':
                            errors.append({
                                'type': '小类parentCode错误',
                                'row': row_num,
                                'code': code,
                                'level': level_zh,
                                'parent_code': parent_code,
                                'issue': f"小类'{code}'的parentCode'{parent_code}'不是中类",
                                'expected': '3位数字代码(中类)',
                                'actual': f"{parent_type}({parent_length}位)"
                            })
                    
                    # 中类应该指向大类
                    elif level_zh == '中类' and code_type == 'medium':
                        if parent_type not in ['major_1digit', 'major_2digit']:
                            errors.append({
                                'type': '中类parentCode错误',
                                'row': row_num,
                                'code': code,
                                'level': level_zh,
                                'parent_code': parent_code,
                                'issue': f"中类'{code}'的parentCode'{parent_code}'不是大类",
                                'expected': '1-2位数字代码(大类)',
                                'actual': f"{parent_type}({parent_length}位)"
                            })
                    
                    # 大类应该指向门类
                    elif level_zh == '大类' and code_type in ['major_1digit', 'major_2digit']:
                        if parent_type != 'category':
                            errors.append({
                                'type': '大类parentCode错误',
                                'row': row_num,
                                'code': code,
                                'level': level_zh,
                                'parent_code': parent_code,
                                'issue': f"大类'{code}'的parentCode'{parent_code}'不是门类",
                                'expected': '字母代码(门类)',
                                'actual': f"{parent_type}({parent_length}位)"
                            })
                
                # 检查3：特殊情况 - 小类直接指向大类（这是最严重的错误）
                if level_zh == '小类' and code_type == 'subclass':
                    if parent_type in ['major_1digit', 'major_2digit']:
                        errors.append({
                            'type': '严重错误：小类直接指向大类',
                            'row': row_num,
                            'code': code,
                            'level': level_zh,
                            'parent_code': parent_code,
                            'issue': f"小类'{code}'直接指向大类'{parent_code}'，跳过了中类层级",
                            'expected': '应指向3位数字代码(中类)',
                            'actual': f"指向{parent_type}({parent_length}位)"
                        })
    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 输出检查结果
    print("\n📊 统计信息:")
    print("-" * 40)
    for key, count in sorted(statistics.items()):
        print(f"{key}: {count}")
    
    print(f"\n🔍 检查结果:")
    print("-" * 40)
    print(f"总共检查了 {row_num-1} 条记录")
    print(f"发现 {len(errors)} 个错误")
    print(f"发现 {len(warnings)} 个警告")
    
    if errors:
        print(f"\n❌ 发现的错误:")
        print("=" * 60)
        
        # 按错误类型分组
        error_by_type = defaultdict(list)
        for error in errors:
            error_by_type[error['type']].append(error)
        
        for error_type, error_list in error_by_type.items():
            print(f"\n🚨 {error_type} ({len(error_list)}个):")
            print("-" * 50)
            
            for error in error_list[:10]:  # 只显示前10个
                print(f"  行 {error['row']}: {error['code']} ({error['level']})")
                print(f"    问题: {error['issue']}")
                print(f"    期望: {error['expected']}")
                print(f"    实际: {error['actual']}")
                if 'parent_code' in error:
                    print(f"    父级代码: {error['parent_code']}")
                print()
            
            if len(error_list) > 10:
                print(f"    ... 还有 {len(error_list) - 10} 个类似错误")
                print()
    
    else:
        print("\n✅ 未发现编码错误！")
    
    # 生成修正建议
    if errors:
        print(f"\n💡 修正建议:")
        print("=" * 60)
        
        serious_errors = [e for e in errors if '严重错误' in e['type']]
        if serious_errors:
            print("🔥 严重错误需要立即修正:")
            print("这些小类直接指向大类，违反了四级分类体系。")
            print("需要添加对应的中类分类，或修正parentCode指向。")
            print()
        
        level_errors = [e for e in errors if 'parentCode错误' in e['type']]
        if level_errors:
            print("⚠️  层级关系错误:")
            print("这些分类的parentCode指向了错误的层级。")
            print("需要检查并修正parentCode，确保符合四级分类体系。")
            print()
        
        format_errors = [e for e in errors if '级别与代码不匹配' in e['type']]
        if format_errors:
            print("📝 格式错误:")
            print("这些分类的代码格式与声明的级别不匹配。")
            print("需要检查代码格式或级别声明是否正确。")
    
    return errors, statistics

if __name__ == "__main__":
    csv_file = "final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv"
    errors, stats = check_csv_encoding_errors(csv_file)
