#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for IndustryMatcher functionality
测试IndustryMatcher功能的脚本
"""

import sys
import os
sys.path.append('..')

from industry_comparison_tool_产业比较分析工具 import DataLoader, IndustryMatcher
from industry_keywords_产业关键词库 import IndustryType

def test_industry_matcher():
    """Test the IndustryMatcher functionality"""
    print("=== Testing IndustryMatcher ===")
    
    try:
        # Initialize components
        print("1. Initializing DataLoader...")
        loader = DataLoader()
        
        print("2. Loading classification data...")
        gb_data = loader.load_gb_data()
        isic_data = loader.load_isic_data()
        print(f"   GB data: {gb_data.shape}")
        print(f"   ISIC data: {isic_data.shape}")
        
        print("3. Creating IndustryMatcher...")
        matcher = IndustryMatcher(loader)
        
        print("4. Testing AI industry matching...")
        ai_matches = matcher.search_by_industry(IndustryType.AI, 'gb', 'fuzzy', 0.2)
        
        if 'gb' in ai_matches:
            print(f"   Found {len(ai_matches['gb'])} AI-related classifications in GB system")
            for i, match in enumerate(ai_matches['gb'][:3]):
                print(f"   {i+1}. [{match['level']}] {match['code']} - {match['name'][:50]}...")
                print(f"      Score: {match['match_score']:.3f}")
        
        print("5. Testing automotive industry matching...")
        auto_matches = matcher.search_by_industry(IndustryType.AUTOMOTIVE, 'gb', 'fuzzy', 0.2)
        
        if 'gb' in auto_matches:
            print(f"   Found {len(auto_matches['gb'])} automotive-related classifications in GB system")
            for i, match in enumerate(auto_matches['gb'][:3]):
                print(f"   {i+1}. [{match['level']}] {match['code']} - {match['name'][:50]}...")
                print(f"      Score: {match['match_score']:.3f}")
        
        print("6. Testing hierarchy matching...")
        hierarchy = matcher.get_hierarchy_matches(IndustryType.AI, 'gb')
        if 'gb' in hierarchy:
            print(f"   AI hierarchy levels in GB: {list(hierarchy['gb'].keys())}")
        
        print("\n=== IndustryMatcher Test Completed Successfully ===")
        return True
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_industry_matcher()
    if success:
        print("All tests passed!")
    else:
        print("Tests failed!")
        sys.exit(1)
