# Industry Comparison Analysis - 产业比较分析工具

## Project Overview - 项目概述

This module provides comprehensive comparison analysis between GB/T 4754-2017 (China's National Industry Classification) and ISIC Rev.5 (International Standard Industrial Classification) for four key industries:

本模块提供GB/T 4754-2017（中国国民经济行业分类）和ISIC Rev.5（国际标准行业分类）之间四个关键产业的全面比较分析：

1. **AI Artificial Intelligence - AI人工智能产业**
2. **Automotive (Autonomous Driving/New Energy Vehicles) - 自动驾驶/新能源汽车产业**
3. **Biomedicine - 生物医药产业**
4. **Clean Technology (Solar/Photovoltaic) - 清洁技术产业（光伏等）**

## Directory Structure - 目录结构

```
industry_comparison_analysis/
├── README_产业比较分析说明.md                    # This documentation file
├── industry_comparison_tool_产业比较分析工具.py   # Main analysis tool
├── industry_keywords_产业关键词库.py              # Industry keywords library
├── comparison_report_template.html               # HTML report template
└── results/                                      # Analysis results output
    ├── ai_industry_comparison.html               # AI industry analysis report
    ├── automotive_industry_comparison.html       # Automotive industry analysis report
    ├── biomedicine_industry_comparison.html      # Biomedicine industry analysis report
    └── clean_technology_comparison.html          # Clean technology analysis report
```

## Key Features - 主要功能

### 1. Multi-level Classification Comparison - 多层级分类对比
- **GB/T 4754-2017**: 门类 (Category) → 大类 (Major) → 中类 (Medium) → 小类 (Subclass)
- **ISIC Rev.5**: Section → Division → Group → Class

### 2. Keyword-based Industry Matching - 基于关键词的产业匹配
- Comprehensive keyword libraries for each industry
- Support for both Chinese and English keywords
- Fuzzy matching and exact matching algorithms

### 3. Visualization and Reporting - 可视化和报告生成
- Interactive charts using Plotly
- Comprehensive HTML reports
- Hierarchical comparison matrices
- Coverage analysis heatmaps

## Usage Instructions - 使用说明

### Command Line Interface - 命令行接口
```bash
# Analyze all four industries
python industry_comparison_tool_产业比较分析工具.py --all

# Analyze specific industry
python industry_comparison_tool_产业比较分析工具.py --industry ai
python industry_comparison_tool_产业比较分析工具.py --industry automotive
python industry_comparison_tool_产业比较分析工具.py --industry biomedicine
python industry_comparison_tool_产业比较分析工具.py --industry clean_tech

# Specify output directory
python industry_comparison_tool_产业比较分析工具.py --all --output ./custom_results/

# Verbose output
python industry_comparison_tool_产业比较分析工具.py --all --verbose
```

## Technical Architecture - 技术架构

### Dependencies - 依赖项
- pandas: Data processing and analysis
- plotly: Interactive visualization
- argparse: Command line interface
- json: Data serialization

### Core Components - 核心组件
1. **DataLoader**: Loads and validates classification data
2. **IndustryKeywords**: Manages industry-specific keyword libraries
3. **IndustryMatcher**: Implements keyword matching algorithms
4. **ClassificationComparator**: Performs cross-classification analysis
5. **IndustryVisualization**: Generates interactive charts
6. **ReportGenerator**: Creates comprehensive HTML reports

## Data Sources - 数据源

- **GB/T 4754-2017**: `../final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv`
- **ISIC Rev.5**: `../final_data/ISIC_Rev5/ISIC_Rev5_en.csv`

## Output Format - 输出格式

### HTML Reports Include - HTML报告包含
- Executive summary
- Industry classification mapping tables
- Hierarchical difference analysis
- Keyword matching results
- Interactive visualization charts
- Conclusions and recommendations

---

*Created: January 2025*  
*Version: 1.0*
