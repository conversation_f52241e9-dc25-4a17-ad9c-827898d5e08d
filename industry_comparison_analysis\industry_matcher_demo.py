#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Industry Matcher Demonstration
产业匹配算法演示

This script demonstrates the key functionality of the IndustryMatcher class
and provides examples of how it identifies industry-related classifications.
"""

# Mock demonstration of IndustryMatcher functionality
def demonstrate_industry_matching():
    """
    Demonstrate the core functionality of IndustryMatcher
    演示IndustryMatcher的核心功能
    """
    
    print("=== Industry Matcher Demonstration ===")
    print("产业分类匹配算法演示")
    print()
    
    # Demonstrate AI Industry Matching
    print("1. AI人工智能产业匹配示例:")
    print("   关键词: 人工智能, 机器学习, 深度学习, 算法, 智能系统, AI, artificial intelligence")
    print()
    print("   GB/T 4754-2017 匹配结果示例:")
    ai_gb_examples = [
        {"code": "6520", "name": "软件开发", "level": "小类", "score": 0.85, "keywords": ["软件", "智能系统"]},
        {"code": "6540", "name": "数据处理和存储服务", "level": "小类", "score": 0.75, "keywords": ["数据", "算法"]},
        {"code": "3591", "name": "智能消费设备制造", "level": "小类", "score": 0.70, "keywords": ["智能", "设备"]},
    ]
    
    for i, match in enumerate(ai_gb_examples, 1):
        print(f"   {i}. [{match['level']}] {match['code']} - {match['name']}")
        print(f"      匹配度: {match['score']:.2f}, 匹配关键词: {match['keywords']}")
    
    print()
    print("   ISIC Rev.5 匹配结果示例:")
    ai_isic_examples = [
        {"code": "6201", "name": "Computer programming activities", "level": "Class", "score": 0.80, "keywords": ["programming", "computer"]},
        {"code": "6202", "name": "Computer consultancy activities", "level": "Class", "score": 0.75, "keywords": ["computer", "consultancy"]},
        {"code": "2620", "name": "Manufacture of computers and peripheral equipment", "level": "Group", "score": 0.65, "keywords": ["computers", "equipment"]},
    ]
    
    for i, match in enumerate(ai_isic_examples, 1):
        print(f"   {i}. [{match['level']}] {match['code']} - {match['name']}")
        print(f"      Score: {match['score']:.2f}, Keywords: {match['keywords']}")
    
    print("\n" + "="*60)
    
    # Demonstrate Automotive Industry Matching
    print("2. 自动驾驶/新能源汽车产业匹配示例:")
    print("   关键词: 新能源汽车, 电动汽车, 自动驾驶, 智能汽车, 充电桩, automotive, electric vehicle")
    print()
    print("   GB/T 4754-2017 匹配结果示例:")
    auto_gb_examples = [
        {"code": "3610", "name": "汽车整车制造", "level": "中类", "score": 0.90, "keywords": ["汽车", "制造"]},
        {"code": "3840", "name": "充电桩制造", "level": "小类", "score": 0.85, "keywords": ["充电桩", "新能源"]},
        {"code": "3670", "name": "汽车零部件及配件制造", "level": "中类", "score": 0.75, "keywords": ["汽车", "零部件"]},
    ]
    
    for i, match in enumerate(auto_gb_examples, 1):
        print(f"   {i}. [{match['level']}] {match['code']} - {match['name']}")
        print(f"      匹配度: {match['score']:.2f}, 匹配关键词: {match['keywords']}")
    
    print()
    print("   ISIC Rev.5 匹配结果示例:")
    auto_isic_examples = [
        {"code": "2910", "name": "Manufacture of motor vehicles", "level": "Group", "score": 0.95, "keywords": ["motor vehicles", "manufacture"]},
        {"code": "2930", "name": "Manufacture of parts and accessories for motor vehicles", "level": "Group", "score": 0.80, "keywords": ["parts", "motor vehicles"]},
        {"code": "4510", "name": "Sale of motor vehicles", "level": "Group", "score": 0.70, "keywords": ["sale", "motor vehicles"]},
    ]
    
    for i, match in enumerate(auto_isic_examples, 1):
        print(f"   {i}. [{match['level']}] {match['code']} - {match['name']}")
        print(f"      Score: {match['score']:.2f}, Keywords: {match['keywords']}")
    
    print("\n" + "="*60)
    
    # Demonstrate Biomedicine Industry Matching
    print("3. 生物医药产业匹配示例:")
    print("   关键词: 生物技术, 生物制药, 医药制造, 医疗器械, 疫苗, biomedicine, pharmaceutical")
    print()
    print("   层级分析示例:")
    
    biomedicine_hierarchy = {
        "GB": {
            "门类": ["C - 制造业"],
            "大类": ["27 - 医药制造业"],
            "中类": ["271 - 化学药品原料药制造", "272 - 化学药品制剂制造", "273 - 中药饮片加工", "274 - 中成药生产", "275 - 兽用药品制造", "276 - 生物药品制造", "277 - 卫生材料及医药用品制造"],
            "小类": ["2761 - 疫苗制造", "2762 - 血液制品制造", "2763 - 基因工程药物和疫苗制造"]
        },
        "ISIC": {
            "Section": ["C - Manufacturing"],
            "Division": ["21 - Manufacture of basic pharmaceutical products and pharmaceutical preparations"],
            "Group": ["210 - Manufacture of basic pharmaceutical products and pharmaceutical preparations"],
            "Class": ["2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations"]
        }
    }
    
    for system, levels in biomedicine_hierarchy.items():
        print(f"   {system} 系统层级覆盖:")
        for level, classifications in levels.items():
            print(f"     {level}: {len(classifications)} 个分类")
            for classification in classifications[:2]:  # Show first 2
                print(f"       - {classification}")
            if len(classifications) > 2:
                print(f"       - ... 等 {len(classifications)-2} 个")
    
    print("\n" + "="*60)
    
    # Demonstrate Clean Technology Industry Matching
    print("4. 清洁技术产业匹配示例:")
    print("   关键词: 太阳能, 光伏, 风能, 清洁能源, 储能, solar, photovoltaic, renewable energy")
    print()
    print("   分类差异分析示例:")
    
    clean_tech_comparison = {
        "太阳能/光伏": {
            "GB": "3825 - 光伏设备及元器件制造",
            "ISIC": "2790 - Manufacture of other electrical equipment",
            "差异": "GB更具体，ISIC较宽泛"
        },
        "风能": {
            "GB": "3411 - 风能原动设备制造", 
            "ISIC": "2811 - Manufacture of engines and turbines",
            "差异": "GB专门分类，ISIC归入通用设备"
        },
        "储能": {
            "GB": "3841 - 锂离子电池制造",
            "ISIC": "2720 - Manufacture of batteries and accumulators", 
            "差异": "GB细分电池类型，ISIC统一归类"
        }
    }
    
    for technology, comparison in clean_tech_comparison.items():
        print(f"   {technology}:")
        print(f"     GB/T 4754-2017: {comparison['GB']}")
        print(f"     ISIC Rev.5: {comparison['ISIC']}")
        print(f"     差异分析: {comparison['差异']}")
        print()
    
    print("="*60)
    print("演示完成 - IndustryMatcher 核心功能展示")
    print("Demonstration Complete - IndustryMatcher Core Functionality Showcase")

if __name__ == "__main__":
    demonstrate_industry_matching()
