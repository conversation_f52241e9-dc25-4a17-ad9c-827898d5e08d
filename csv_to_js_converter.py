#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV to JavaScript converter for GB/T 4754-2017 data
Applies corrections to the original CSV data and generates corrected JavaScript data file
"""

import csv
import json
import re

def clean_note_field(note):
    """Clean the note field by removing #VALUE! and other Excel artifacts"""
    if not note or note.strip() == '#VALUE!' or note.strip() == '':
        return ''
    return note.strip()

def apply_corrections(data):
    """Apply corrections to the data based on identified issues"""
    corrections = {
        # Coal mining corrections - fix parentCode from "54" to correct values
        "610": {"parentCode": "61", "add_medium": {"code": "61", "nameZh": "烟煤和无烟煤开采洗选", "nameEn": "Bituminous coal and anthracite mining and washing", "parentCode": "6"}},
        "620": {"parentCode": "62", "add_medium": {"code": "62", "nameZh": "褐煤开采洗选", "nameEn": "Lignite mining and washing", "parentCode": "6"}},
        "690": {"parentCode": "69", "add_medium": {"code": "69", "nameZh": "其他煤炭采选", "nameEn": "Other coal mining and dressing", "parentCode": "6"}},
        
        # Ferrous metal mining corrections - fix parentCode from "72" to correct values
        "810": {"parentCode": "81", "add_medium": {"code": "81", "nameZh": "铁矿采选", "nameEn": "Iron ore mining and processing", "parentCode": "8"}},
        "820": {"parentCode": "82", "add_medium": {"code": "82", "nameZh": "锰矿、铬矿采选", "nameEn": "Manganese ore and chromium ore mining and dressing", "parentCode": "8"}},
        "890": {"parentCode": "89", "add_medium": {"code": "89", "nameZh": "其他黑色金属矿采选", "nameEn": "Other ferrous metal mining and dressing", "parentCode": "8"}},
        
        # Mining support activities corrections - fix parentCode from "109" to "11"
        "1110": {"parentCode": "11"},
        "1120": {"parentCode": "11"},
        "1190": {"parentCode": "11"},
        
        # Other mining corrections - fix parentCode from "109" to "12"
        "1200": {"parentCode": "12"},
        
        # Tobacco industry corrections - fix parentCode from "152" to correct values
        "1610": {"parentCode": "161", "add_medium": {"code": "161", "nameZh": "烟叶复烤", "nameEn": "Tobacco redrying", "parentCode": "16"}},
        "1620": {"parentCode": "162", "add_medium": {"code": "162", "nameZh": "卷烟制造", "nameEn": "Cigarette manufacturing", "parentCode": "16"}},
        "1690": {"parentCode": "169", "add_medium": {"code": "169", "nameZh": "其他烟草制品制造", "nameEn": "Other tobacco product manufacturing", "parentCode": "16"}},
        
        # Sugar industry corrections - fix parentCode from "133" to "134"
        "1340": {"parentCode": "134", "add_medium": {"code": "134", "nameZh": "制糖业", "nameEn": "Sugar industry", "parentCode": "13"}},
    }
    
    corrected_data = []
    added_mediums = set()
    
    for row in data:
        code = row['Code']
        
        # Apply corrections if this code needs fixing
        if code in corrections:
            correction = corrections[code]
            
            # Add medium level classification if needed
            if 'add_medium' in correction and correction['add_medium']['code'] not in added_mediums:
                medium_row = {
                    'Category_level': 'Medium',
                    'Code': correction['add_medium']['code'],
                    'Name': correction['add_medium']['nameZh'],
                    'Name_en': correction['add_medium']['nameEn'],
                    'Parent_code': correction['add_medium']['parentCode'],
                    'Core_keyword': row['Core_keyword'],
                    'Note': row['Note']
                }
                corrected_data.append(medium_row)
                added_mediums.add(correction['add_medium']['code'])
            
            # Fix the parentCode
            row['Parent_code'] = correction['parentCode']
        
        corrected_data.append(row)
    
    return corrected_data

def csv_to_js(csv_file_path, js_file_path):
    """Convert CSV file to JavaScript data file with corrections applied"""
    
    data = []
    
    # Read CSV file
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        # Skip BOM if present
        content = csvfile.read()
        if content.startswith('\ufeff'):
            content = content[1:]
        
        reader = csv.DictReader(content.splitlines())
        
        for row in reader:
            # Map CSV columns to our data structure
            data_row = {
                'level': row['分类级别'],
                'code': row['分类代码'],
                'nameZh': row['分类名称'],
                'nameEn': row['Name'],
                'parentCode': row['上级代码'],
                'keywords': row['核心关键词'],
                'note': clean_note_field(row['说明'])
            }
            
            # Convert level names to English
            level_mapping = {
                '门类': 'Category',
                '大类': 'Major', 
                '中类': 'Medium',
                '小类': 'Subclass'
            }
            data_row['level'] = level_mapping.get(data_row['level'], data_row['level'])
            
            data.append(data_row)
    
    # Apply corrections
    print("Applying corrections to data...")
    
    # Create a mapping for easy lookup
    code_to_row = {row['code']: row for row in data}
    
    # Apply specific corrections
    corrections_applied = []
    
    # Coal mining corrections
    if '610' in code_to_row and code_to_row['610']['parentCode'] == '54':
        # Add medium level classifications
        medium_61 = {
            'level': 'Medium',
            'code': '61',
            'nameZh': '烟煤和无烟煤开采洗选',
            'nameEn': 'Bituminous coal and anthracite mining and washing',
            'parentCode': '6',
            'keywords': code_to_row['610']['keywords'],
            'note': code_to_row['610']['note']
        }
        medium_62 = {
            'level': 'Medium', 
            'code': '62',
            'nameZh': '褐煤开采洗选',
            'nameEn': 'Lignite mining and washing',
            'parentCode': '6',
            'keywords': code_to_row['620']['keywords'] if '620' in code_to_row else '',
            'note': code_to_row['620']['note'] if '620' in code_to_row else ''
        }
        medium_69 = {
            'level': 'Medium',
            'code': '69', 
            'nameZh': '其他煤炭采选',
            'nameEn': 'Other coal mining and dressing',
            'parentCode': '6',
            'keywords': code_to_row['690']['keywords'] if '690' in code_to_row else '',
            'note': code_to_row['690']['note'] if '690' in code_to_row else ''
        }
        
        # Insert medium levels and fix parent codes
        data.append(medium_61)
        data.append(medium_62) 
        data.append(medium_69)
        
        code_to_row['610']['parentCode'] = '61'
        code_to_row['620']['parentCode'] = '62'
        code_to_row['690']['parentCode'] = '69'
        
        corrections_applied.extend(['610->61', '620->62', '690->69'])
    
    # Ferrous metal mining corrections
    if '810' in code_to_row and code_to_row['810']['parentCode'] == '72':
        # Add medium level classifications
        medium_81 = {
            'level': 'Medium',
            'code': '81',
            'nameZh': '铁矿采选', 
            'nameEn': 'Iron ore mining and processing',
            'parentCode': '8',
            'keywords': code_to_row['810']['keywords'],
            'note': code_to_row['810']['note']
        }
        medium_82 = {
            'level': 'Medium',
            'code': '82',
            'nameZh': '锰矿、铬矿采选',
            'nameEn': 'Manganese ore and chromium ore mining and dressing', 
            'parentCode': '8',
            'keywords': code_to_row['820']['keywords'] if '820' in code_to_row else '',
            'note': code_to_row['820']['note'] if '820' in code_to_row else ''
        }
        medium_89 = {
            'level': 'Medium',
            'code': '89',
            'nameZh': '其他黑色金属矿采选',
            'nameEn': 'Other ferrous metal mining and dressing',
            'parentCode': '8', 
            'keywords': code_to_row['890']['keywords'] if '890' in code_to_row else '',
            'note': code_to_row['890']['note'] if '890' in code_to_row else ''
        }
        
        data.append(medium_81)
        data.append(medium_82)
        data.append(medium_89)
        
        code_to_row['810']['parentCode'] = '81'
        code_to_row['820']['parentCode'] = '82' 
        code_to_row['890']['parentCode'] = '89'
        
        corrections_applied.extend(['810->81', '820->82', '890->89'])
    
    # Mining support activities corrections
    if '1110' in code_to_row and code_to_row['1110']['parentCode'] == '109':
        code_to_row['1110']['parentCode'] = '11'
        code_to_row['1120']['parentCode'] = '11'
        code_to_row['1190']['parentCode'] = '11'
        corrections_applied.extend(['1110->11', '1120->11', '1190->11'])
    
    # Other mining corrections
    if '1200' in code_to_row and code_to_row['1200']['parentCode'] == '109':
        code_to_row['1200']['parentCode'] = '12'
        corrections_applied.append('1200->12')
    
    print(f"Applied corrections: {', '.join(corrections_applied)}")
    
    # Generate JavaScript file
    with open(js_file_path, 'w', encoding='utf-8') as jsfile:
        jsfile.write('// GB/T 4754-2017 国民经济行业分类标准数据 - 修正版\n')
        jsfile.write('// 修正了原始数据中的分类层级错误和parentCode错误\n')
        jsfile.write('// 修正日期: 2025年1月\n\n')
        jsfile.write('const csvData = [\n')
        
        for i, row in enumerate(data):
            jsfile.write('    {')
            jsfile.write(f'level:"{row["level"]}",')
            jsfile.write(f'code:"{row["code"]}",')
            jsfile.write(f'nameZh:"{row["nameZh"]}",')
            jsfile.write(f'nameEn:"{row["nameEn"]}",')
            jsfile.write(f'parentCode:"{row["parentCode"]}",')
            jsfile.write(f'keywords:"{row["keywords"]}",')
            jsfile.write(f'note:"{row["note"]}"')
            jsfile.write('}')
            
            if i < len(data) - 1:
                jsfile.write(',')
            jsfile.write('\n')
        
        jsfile.write('];\n')
    
    print(f"Successfully converted {len(data)} records to {js_file_path}")
    print(f"Applied {len(corrections_applied)} corrections")

if __name__ == "__main__":
    csv_file = "final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv"
    js_file = "gb_t_4754_data_corrected.js"
    
    csv_to_js(csv_file, js_file)
