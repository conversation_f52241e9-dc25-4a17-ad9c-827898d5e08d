<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>industry_comparison_analysis_report</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
</style>


<script src="Industry_Comparison_Analysis_Report_files/libs/clipboard/clipboard.min.js"></script>
<script src="Industry_Comparison_Analysis_Report_files/libs/quarto-html/quarto.js"></script>
<script src="Industry_Comparison_Analysis_Report_files/libs/quarto-html/popper.min.js"></script>
<script src="Industry_Comparison_Analysis_Report_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="Industry_Comparison_Analysis_Report_files/libs/quarto-html/anchor.min.js"></script>
<link href="Industry_Comparison_Analysis_Report_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="Industry_Comparison_Analysis_Report_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="Industry_Comparison_Analysis_Report_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="Industry_Comparison_Analysis_Report_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="Industry_Comparison_Analysis_Report_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content"><header id="title-block-header" class="quarto-title-block"></header>




<section id="industry-comparison-analysis-report" class="level1">
<h1>Industry Comparison Analysis Report</h1>
</section>
<section id="产业比较分析报告" class="level1">
<h1>产业比较分析报告</h1>
<p><strong>Generated Date:</strong> January 2025<br>
<strong>Analysis Tool:</strong> IndustryMatcher<br>
<strong>Classification Standards:</strong> GB/T 4754-2017 vs ISIC Rev.5</p>
<hr>
<section id="executive-summary-执行摘要" class="level2">
<h2 class="anchored" data-anchor-id="executive-summary-执行摘要">Executive Summary 执行摘要</h2>
<p>This report demonstrates the comprehensive analysis of four key industries across two major classification standards: China’s GB/T 4754-2017 and the international ISIC Rev.5. The analysis reveals significant differences in classification granularity, industry coverage, and categorization approaches.</p>
<p>本报告展示了四个关键产业在两个主要分类标准中的全面分析：中国GB/T 4754-2017和国际ISIC Rev.5标准。分析揭示了分类粒度、产业覆盖范围和分类方法的显著差异。</p>
<hr>
</section>
<section id="ai-artificial-intelligence-industry-人工智能产业" class="level2">
<h2 class="anchored" data-anchor-id="ai-artificial-intelligence-industry-人工智能产业">1. AI Artificial Intelligence Industry 人工智能产业</h2>
<section id="industry-keywords-产业关键词" class="level3">
<h3 class="anchored" data-anchor-id="industry-keywords-产业关键词">Industry Keywords 产业关键词</h3>
<ul>
<li><strong>Chinese:</strong> 人工智能, 机器学习, 深度学习, 算法, 智能系统, 数据挖掘, 计算机视觉, 自然语言处理</li>
<li><strong>English:</strong> artificial intelligence, AI, machine learning, deep learning, neural network, algorithm, intelligent system, computer vision, natural language processing</li>
</ul>
</section>
<section id="classification-analysis-分类分析" class="level3">
<h3 class="anchored" data-anchor-id="classification-analysis-分类分析">Classification Analysis 分类分析</h3>
<section id="gbt-4754-2017-matches" class="level4">
<h4 class="anchored" data-anchor-id="gbt-4754-2017-matches">GB/T 4754-2017 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 8%">
<col style="width: 20%">
<col style="width: 20%">
<col style="width: 19%">
<col style="width: 20%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Chinese Name</th>
<th>English Name</th>
<th>Match Score</th>
<th>Key Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>大类</td>
<td>65</td>
<td>软件和信息技术服务业</td>
<td>Software and Information Technology Services</td>
<td>0.95</td>
<td>软件, 信息技术服务</td>
</tr>
<tr class="even">
<td>中类</td>
<td>651</td>
<td>软件开发</td>
<td>Software Development</td>
<td>0.98</td>
<td>软件开发</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>6511</td>
<td>基础软件开发</td>
<td>Basic Software Development</td>
<td>0.92</td>
<td>基础软件开发</td>
</tr>
<tr class="even">
<td>小类</td>
<td>6512</td>
<td>支撑软件开发</td>
<td>Support Software Development</td>
<td>0.88</td>
<td>支撑软件开发</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>6513</td>
<td>应用软件开发</td>
<td>Application Software Development</td>
<td>0.95</td>
<td>应用软件开发</td>
</tr>
<tr class="even">
<td>小类</td>
<td>6520</td>
<td>集成电路设计</td>
<td>Integrated Circuit Design</td>
<td>0.85</td>
<td>集成电路设计</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>6540</td>
<td>运行维护服务</td>
<td>Operation and Maintenance Services</td>
<td>0.80</td>
<td>运行维护服务</td>
</tr>
<tr class="even">
<td>小类</td>
<td>6550</td>
<td>信息处理和存储支持服务</td>
<td>Information Processing and Storage Support Services</td>
<td>0.88</td>
<td>信息处理, 存储</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>6450</td>
<td>互联网数据服务</td>
<td>Internet Data Services</td>
<td>0.85</td>
<td>数据处理, 云计算</td>
</tr>
</tbody>
</table>
</section>
<section id="isic-rev.5-matches" class="level4">
<h4 class="anchored" data-anchor-id="isic-rev.5-matches">ISIC Rev.5 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 9%">
<col style="width: 28%">
<col style="width: 19%">
<col style="width: 31%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Classification Name</th>
<th>Match Score</th>
<th>Key Matched Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Section</td>
<td>K</td>
<td>Telecommunications, computer programming, consultancy, computing infrastructure and other information service activities</td>
<td>0.90</td>
<td>telecommunications, computer programming, computing</td>
</tr>
<tr class="even">
<td>Division</td>
<td>62</td>
<td>Computer programming, consultancy and related activities</td>
<td>0.85</td>
<td>computer programming, consultancy</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>6201</td>
<td>Computer programming activities</td>
<td>0.80</td>
<td>programming, computer</td>
</tr>
<tr class="even">
<td>Group</td>
<td>261</td>
<td>Manufacture of electronic components and boards</td>
<td>0.70</td>
<td>electronic components, semiconductors</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2610</td>
<td>Manufacture of electronic components and boards</td>
<td>0.65</td>
<td>electronic components, circuit boards</td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="key-findings-主要发现" class="level3">
<h3 class="anchored" data-anchor-id="key-findings-主要发现">Key Findings 主要发现</h3>
<ul>
<li><strong>GB/T 4754-2017</strong> provides more specific AI-related classifications, particularly in software development and data processing</li>
<li><strong>ISIC Rev.5</strong> tends to group AI activities under broader computer-related categories</li>
<li>Both systems show coverage gaps for emerging AI technologies like machine learning platforms</li>
</ul>
<hr>
</section>
</section>
<section id="automotive-industry-autonomous-drivingnew-energy-vehicles-自动驾驶新能源汽车产业" class="level2">
<h2 class="anchored" data-anchor-id="automotive-industry-autonomous-drivingnew-energy-vehicles-自动驾驶新能源汽车产业">2. Automotive Industry (Autonomous Driving/New Energy Vehicles) 自动驾驶/新能源汽车产业</h2>
<section id="industry-keywords-产业关键词-1" class="level3">
<h3 class="anchored" data-anchor-id="industry-keywords-产业关键词-1">Industry Keywords 产业关键词</h3>
<ul>
<li><strong>Chinese:</strong> 新能源汽车, 电动汽车, 自动驾驶, 智能汽车, 充电桩, 动力电池, 车联网, 激光雷达</li>
<li><strong>English:</strong> new energy vehicle, electric vehicle, autonomous driving, smart vehicle, charging station, automotive, motor vehicle</li>
</ul>
</section>
<section id="classification-analysis-分类分析-1" class="level3">
<h3 class="anchored" data-anchor-id="classification-analysis-分类分析-1">Classification Analysis 分类分析</h3>
<section id="gbt-4754-2017-matches-1" class="level4">
<h4 class="anchored" data-anchor-id="gbt-4754-2017-matches-1">GB/T 4754-2017 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 8%">
<col style="width: 20%">
<col style="width: 20%">
<col style="width: 19%">
<col style="width: 20%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Chinese Name</th>
<th>English Name</th>
<th>Match Score</th>
<th>Key Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>大类</td>
<td>36</td>
<td>汽车制造业</td>
<td>Automotive Manufacturing</td>
<td>0.95</td>
<td>汽车制造业, 制造</td>
</tr>
<tr class="even">
<td>中类</td>
<td>361</td>
<td>汽车整车制造</td>
<td>Automobile Manufacturing</td>
<td>0.92</td>
<td>汽车, 整车制造</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>3611</td>
<td>汽柴油车整车制造</td>
<td>Gasoline and Diesel Vehicle Manufacturing</td>
<td>0.88</td>
<td>汽车, 整车制造</td>
</tr>
<tr class="even">
<td>小类</td>
<td>3612</td>
<td>新能源车整车制造</td>
<td>New Energy Vehicle Manufacturing</td>
<td>0.98</td>
<td>新能源汽车, 整车制造</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>3670</td>
<td>汽车零部件及配件制造</td>
<td>Auto Parts and Accessories Manufacturing</td>
<td>0.85</td>
<td>汽车, 零部件</td>
</tr>
<tr class="even">
<td>小类</td>
<td>3840</td>
<td>充电桩制造</td>
<td>Charging Station Manufacturing</td>
<td>0.90</td>
<td>充电桩, 新能源</td>
</tr>
</tbody>
</table>
</section>
<section id="isic-rev.5-matches-1" class="level4">
<h4 class="anchored" data-anchor-id="isic-rev.5-matches-1">ISIC Rev.5 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 9%">
<col style="width: 28%">
<col style="width: 19%">
<col style="width: 31%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Classification Name</th>
<th>Match Score</th>
<th>Key Matched Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Division</td>
<td>29</td>
<td>Manufacture of motor vehicles, trailers and semi-trailers</td>
<td>0.95</td>
<td>motor vehicles, manufacture</td>
</tr>
<tr class="even">
<td>Group</td>
<td>291</td>
<td>Manufacture of motor vehicles</td>
<td>0.95</td>
<td>motor vehicles, manufacture</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2910</td>
<td>Manufacture of motor vehicles</td>
<td>0.95</td>
<td>motor vehicles, manufacture</td>
</tr>
<tr class="even">
<td>Group</td>
<td>292</td>
<td>Manufacture of bodies (coachwork) for motor vehicles</td>
<td>0.85</td>
<td>motor vehicles, bodies</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2920</td>
<td>Manufacture of bodies (coachwork) for motor vehicles</td>
<td>0.85</td>
<td>motor vehicles, bodies</td>
</tr>
<tr class="even">
<td>Group</td>
<td>293</td>
<td>Manufacture of parts and accessories for motor vehicles</td>
<td>0.80</td>
<td>parts, motor vehicles</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2930</td>
<td>Manufacture of parts and accessories for motor vehicles</td>
<td>0.80</td>
<td>parts, motor vehicles</td>
</tr>
<tr class="even">
<td>Group</td>
<td>466</td>
<td>Wholesale of motor vehicles, motorcycles and related parts</td>
<td>0.75</td>
<td>motor vehicles, wholesale</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>4661</td>
<td>Wholesale of motor vehicles</td>
<td>0.75</td>
<td>motor vehicles, wholesale</td>
</tr>
<tr class="even">
<td>Group</td>
<td>478</td>
<td>Retail sale of motor vehicles, motorcycles and related parts</td>
<td>0.70</td>
<td>motor vehicles, retail</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>4781</td>
<td>Retail sale of motor vehicles</td>
<td>0.70</td>
<td>motor vehicles, retail</td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="key-findings-主要发现-1" class="level3">
<h3 class="anchored" data-anchor-id="key-findings-主要发现-1">Key Findings 主要发现</h3>
<ul>
<li><strong>GB/T 4754-2017</strong> provides detailed automotive classification hierarchy:
<ul>
<li>Separates traditional vehicles (3611) from new energy vehicles (3612)</li>
<li>Includes specific categories for charging infrastructure (3840)</li>
<li>Covers complete automotive supply chain (parts, accessories)</li>
</ul></li>
<li><strong>ISIC Rev.5</strong> provides comprehensive automotive value chain coverage:
<ul>
<li>Manufacturing (Division 29): Complete vehicle production chain</li>
<li>Distribution (Groups 466, 478): Wholesale and retail sales</li>
<li>Services (Group 953): Repair and maintenance</li>
<li>But lacks distinction between traditional and new energy vehicles</li>
</ul></li>
<li><strong>Critical Difference</strong>: GB explicitly recognizes NEV as separate category (3612), ISIC groups all vehicles under 2910</li>
<li><strong>Technology Adaptation</strong>: GB standard shows superior adaptation to automotive industry evolution</li>
</ul>
<hr>
</section>
</section>
<section id="biomedicine-industry-生物医药产业" class="level2">
<h2 class="anchored" data-anchor-id="biomedicine-industry-生物医药产业">3. Biomedicine Industry 生物医药产业</h2>
<section id="industry-keywords-产业关键词-2" class="level3">
<h3 class="anchored" data-anchor-id="industry-keywords-产业关键词-2">Industry Keywords 产业关键词</h3>
<ul>
<li><strong>Chinese:</strong> 生物技术, 生物制药, 医药制造, 医疗器械, 疫苗, 抗体, 基因工程, 细胞治疗</li>
<li><strong>English:</strong> biotechnology, biopharmaceutical, pharmaceutical manufacturing, medical device, vaccine, antibody, genetic engineering</li>
</ul>
</section>
<section id="classification-analysis-分类分析-2" class="level3">
<h3 class="anchored" data-anchor-id="classification-analysis-分类分析-2">Classification Analysis 分类分析</h3>
<section id="gbt-4754-2017-matches-2" class="level4">
<h4 class="anchored" data-anchor-id="gbt-4754-2017-matches-2">GB/T 4754-2017 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 8%">
<col style="width: 20%">
<col style="width: 20%">
<col style="width: 19%">
<col style="width: 20%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Chinese Name</th>
<th>English Name</th>
<th>Match Score</th>
<th>Key Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>门类</td>
<td>C</td>
<td>制造业</td>
<td>Manufacturing</td>
<td>1.00</td>
<td>制造, 生产</td>
</tr>
<tr class="even">
<td>大类</td>
<td>27</td>
<td>医药制造业</td>
<td>Pharmaceutical Manufacturing</td>
<td>0.95</td>
<td>医药, 制造</td>
</tr>
<tr class="odd">
<td>中类</td>
<td>271</td>
<td>化学药品原料药制造</td>
<td>Chemical API Manufacturing</td>
<td>0.90</td>
<td>化学药品, 原料药</td>
</tr>
<tr class="even">
<td>中类</td>
<td>272</td>
<td>化学药品制剂制造</td>
<td>Chemical Preparation Manufacturing</td>
<td>0.90</td>
<td>化学药品, 制剂</td>
</tr>
<tr class="odd">
<td>中类</td>
<td>276</td>
<td>生物药品制造</td>
<td>Biological Drug Manufacturing</td>
<td>0.95</td>
<td>生物药品, 生物技术</td>
</tr>
<tr class="even">
<td>小类</td>
<td>2761</td>
<td>疫苗制造</td>
<td>Vaccine Manufacturing</td>
<td>0.98</td>
<td>疫苗, 生物制品</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>2762</td>
<td>血液制品制造</td>
<td>Blood Product Manufacturing</td>
<td>0.92</td>
<td>血液制品, 生物制品</td>
</tr>
<tr class="even">
<td>小类</td>
<td>2763</td>
<td>基因工程药物和疫苗制造</td>
<td>Genetic Engineering Drug and Vaccine Manufacturing</td>
<td>0.96</td>
<td>基因工程, 生物技术</td>
</tr>
</tbody>
</table>
</section>
<section id="isic-rev.5-matches-2" class="level4">
<h4 class="anchored" data-anchor-id="isic-rev.5-matches-2">ISIC Rev.5 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 9%">
<col style="width: 28%">
<col style="width: 19%">
<col style="width: 31%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Classification Name</th>
<th>Match Score</th>
<th>Key Matched Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Section</td>
<td>C</td>
<td>Manufacturing</td>
<td>0.85</td>
<td>manufacturing, production</td>
</tr>
<tr class="even">
<td>Division</td>
<td>21</td>
<td>Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>0.95</td>
<td>pharmaceutical, manufacture</td>
</tr>
<tr class="odd">
<td>Group</td>
<td>210</td>
<td>Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>0.95</td>
<td>pharmaceutical, basic products</td>
</tr>
<tr class="even">
<td>Class</td>
<td>2100</td>
<td>Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>0.95</td>
<td>pharmaceutical, preparations</td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="key-findings-主要发现-2" class="level3">
<h3 class="anchored" data-anchor-id="key-findings-主要发现-2">Key Findings 主要发现</h3>
<ul>
<li><strong>GB/T 4754-2017</strong> provides much more granular classification for biomedicine</li>
<li><strong>ISIC Rev.5</strong> groups all pharmaceutical activities under a single broad category</li>
<li>GB standard better reflects the complexity and diversity of modern biomedicine industry</li>
</ul>
<hr>
</section>
</section>
<section id="clean-technology-industry-清洁技术产业" class="level2">
<h2 class="anchored" data-anchor-id="clean-technology-industry-清洁技术产业">4. Clean Technology Industry 清洁技术产业</h2>
<section id="industry-keywords-产业关键词-3" class="level3">
<h3 class="anchored" data-anchor-id="industry-keywords-产业关键词-3">Industry Keywords 产业关键词</h3>
<ul>
<li><strong>Chinese:</strong> 太阳能, 光伏, 风能, 清洁能源, 储能, 新能源, 可再生能源, 节能环保</li>
<li><strong>English:</strong> solar energy, photovoltaic, wind energy, clean energy, renewable energy, energy storage, environmental protection</li>
</ul>
</section>
<section id="classification-analysis-分类分析-3" class="level3">
<h3 class="anchored" data-anchor-id="classification-analysis-分类分析-3">Classification Analysis 分类分析</h3>
<section id="gbt-4754-2017-matches-3" class="level4">
<h4 class="anchored" data-anchor-id="gbt-4754-2017-matches-3">GB/T 4754-2017 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 8%">
<col style="width: 20%">
<col style="width: 20%">
<col style="width: 19%">
<col style="width: 20%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Chinese Name</th>
<th>English Name</th>
<th>Match Score</th>
<th>Key Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>小类</td>
<td>3415</td>
<td>风能原动设备制造</td>
<td>Wind Power Equipment Manufacturing</td>
<td>0.98</td>
<td>风能发电设备, 风能原动设备</td>
</tr>
<tr class="even">
<td>小类</td>
<td>3562</td>
<td>半导体器件专用设备制造</td>
<td>Semiconductor Device Equipment Manufacturing</td>
<td>0.92</td>
<td>太阳能电池片设备</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>3862</td>
<td>太阳能器具制造</td>
<td>Solar Equipment Manufacturing</td>
<td>0.95</td>
<td>太阳能器具</td>
</tr>
<tr class="even">
<td>小类</td>
<td>4415</td>
<td>风力发电</td>
<td>Wind Power Generation</td>
<td>0.95</td>
<td>风力发电</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>4416</td>
<td>太阳能发电</td>
<td>Solar Power Generation</td>
<td>0.98</td>
<td>太阳能发电</td>
</tr>
<tr class="even">
<td>小类</td>
<td>4417</td>
<td>生物质能发电</td>
<td>Biomass Power Generation</td>
<td>0.88</td>
<td>生物质能发电</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>4874</td>
<td>风能发电工程施工</td>
<td>Wind Power Engineering Construction</td>
<td>0.90</td>
<td>风能发电工程</td>
</tr>
<tr class="even">
<td>小类</td>
<td>4875</td>
<td>太阳能发电工程施工</td>
<td>Solar Power Engineering Construction</td>
<td>0.92</td>
<td>太阳能发电工程</td>
</tr>
<tr class="odd">
<td>小类</td>
<td>7515</td>
<td>新能源技术推广服务</td>
<td>New Energy Technology Promotion Services</td>
<td>0.85</td>
<td>新能源技术推广</td>
</tr>
</tbody>
</table>
</section>
<section id="isic-rev.5-matches-3" class="level4">
<h4 class="anchored" data-anchor-id="isic-rev.5-matches-3">ISIC Rev.5 Matches</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 10%">
<col style="width: 9%">
<col style="width: 28%">
<col style="width: 19%">
<col style="width: 31%">
</colgroup>
<thead>
<tr class="header">
<th>Level</th>
<th>Code</th>
<th>Classification Name</th>
<th>Match Score</th>
<th>Key Matched Keywords</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Class</td>
<td>2611</td>
<td>Manufacture of solar cells, solar panels and photovoltaic inverters</td>
<td>0.98</td>
<td>solar cells, solar panels, photovoltaic</td>
</tr>
<tr class="even">
<td>Class</td>
<td>3512</td>
<td>Electric power generation activities from renewable sources</td>
<td>0.95</td>
<td>renewable sources, electric power generation</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2811</td>
<td>Manufacture of engines and turbines, except aircraft, vehicle and cycle engines</td>
<td>0.75</td>
<td>engines, turbines</td>
</tr>
<tr class="even">
<td>Group</td>
<td>279</td>
<td>Manufacture of other electrical equipment</td>
<td>0.70</td>
<td>electrical equipment, manufacture</td>
</tr>
<tr class="odd">
<td>Class</td>
<td>2790</td>
<td>Manufacture of other electrical equipment</td>
<td>0.65</td>
<td>electrical equipment</td>
</tr>
<tr class="even">
<td>Class</td>
<td>3511</td>
<td>Electric power generation activities from non-renewable sources</td>
<td>0.60</td>
<td>electric power generation</td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="comparative-analysis-对比分析" class="level3">
<h3 class="anchored" data-anchor-id="comparative-analysis-对比分析">Comparative Analysis 对比分析</h3>
<section id="ai-industry-cross-standard-comparison-ai产业跨标准对比" class="level4">
<h4 class="anchored" data-anchor-id="ai-industry-cross-standard-comparison-ai产业跨标准对比">1. AI Industry Cross-Standard Comparison AI产业跨标准对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 26%">
<col style="width: 22%">
<col style="width: 16%">
<col style="width: 33%">
</colgroup>
<thead>
<tr class="header">
<th>AI Technology Area</th>
<th>GB/T 4754-2017</th>
<th>ISIC Rev.5</th>
<th>Classification Approach</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Software Development</td>
<td>651 软件开发 (6511基础/6512支撑/6513应用)</td>
<td>Division 62 - Computer programming, consultancy and related activities</td>
<td>GB: 3-tier detailed hierarchy; ISIC: Broad division</td>
</tr>
<tr class="even">
<td>AI Hardware</td>
<td>6520 - 集成电路设计 (Integrated Circuit Design)</td>
<td>Group 261/Class 2610 - Manufacture of electronic components and boards</td>
<td>GB: Circuit design focus; ISIC: General electronic components</td>
</tr>
<tr class="odd">
<td>Data Processing</td>
<td>6550 - 信息处理和存储支持服务</td>
<td>Class 6201 - Computer programming activities</td>
<td>GB: Specific data services; ISIC: General programming</td>
</tr>
<tr class="even">
<td>IT Services</td>
<td>6540 - 运行维护服务</td>
<td>Section K - Telecommunications, computer programming, consultancy…</td>
<td>GB: Specific maintenance; ISIC: Comprehensive IT sector</td>
</tr>
<tr class="odd">
<td>Internet Data</td>
<td>6450 - 互联网数据服务</td>
<td>Class 6201 - Computer programming activities</td>
<td>GB: Internet-specific data; ISIC: General programming</td>
</tr>
</tbody>
</table>
<p><strong>Key Difference</strong>: GB provides detailed AI development hierarchy with specific categories for each layer; ISIC uses broader computer programming and IT service categories.</p>
</section>
<section id="automotive-industry-cross-standard-comparison-汽车产业跨标准对比" class="level4">
<h4 class="anchored" data-anchor-id="automotive-industry-cross-standard-comparison-汽车产业跨标准对比">2. Automotive Industry Cross-Standard Comparison 汽车产业跨标准对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 26%">
<col style="width: 21%">
<col style="width: 16%">
<col style="width: 35%">
</colgroup>
<thead>
<tr class="header">
<th>Vehicle Technology</th>
<th>GB/T 4754-2017</th>
<th>ISIC Rev.5</th>
<th>Classification Innovation</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Traditional Vehicles</td>
<td>3611 - 汽柴油车整车制造 (Gasoline and Diesel Vehicle Manufacturing)</td>
<td>Class 2910 - Manufacture of motor vehicles</td>
<td>GB: Separates by fuel type; ISIC: All vehicles together</td>
</tr>
<tr class="even">
<td>New Energy Vehicles</td>
<td>3612 - 新能源车整车制造 ⭐ (New Energy Vehicle Manufacturing)</td>
<td>Class 2910 - Manufacture of motor vehicles</td>
<td>GB: Revolutionary separation; ISIC: No distinction</td>
</tr>
<tr class="odd">
<td>Auto Parts</td>
<td>3670 - 汽车零部件及配件制造 (Auto Parts and Accessories Manufacturing)</td>
<td>Class 2930 - Manufacture of parts and accessories for motor vehicles</td>
<td>Similar coverage and approach</td>
</tr>
<tr class="even">
<td>Vehicle Bodies</td>
<td>No specific equivalent</td>
<td>Class 2920 - Manufacture of bodies (coachwork) for motor vehicles</td>
<td>ISIC advantage: Specific body manufacturing</td>
</tr>
<tr class="odd">
<td>EV Infrastructure</td>
<td>3840 - 充电桩制造 (Charging Station Manufacturing)</td>
<td>No specific equivalent</td>
<td>GB advantage: EV infrastructure recognition</td>
</tr>
<tr class="even">
<td>Vehicle Wholesale</td>
<td>No specific equivalent</td>
<td>Class 4661 - Wholesale of motor vehicles</td>
<td>ISIC advantage: Wholesale coverage</td>
</tr>
<tr class="odd">
<td>Vehicle Retail</td>
<td>No specific equivalent</td>
<td>Class 4781 - Retail sale of motor vehicles</td>
<td>ISIC advantage: Complete value chain</td>
</tr>
</tbody>
</table>
<p><strong>Key Innovation</strong>: GB’s 3612 classification represents the first explicit separation of new energy vehicles from traditional vehicles, while ISIC provides comprehensive value chain coverage.</p>
</section>
<section id="biomedicine-industry-cross-standard-comparison-生物医药产业跨标准对比" class="level4">
<h4 class="anchored" data-anchor-id="biomedicine-industry-cross-standard-comparison-生物医药产业跨标准对比">3. Biomedicine Industry Cross-Standard Comparison 生物医药产业跨标准对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 28%">
<col style="width: 22%">
<col style="width: 17%">
<col style="width: 31%">
</colgroup>
<thead>
<tr class="header">
<th>Pharmaceutical Area</th>
<th>GB/T 4754-2017</th>
<th>ISIC Rev.5</th>
<th>Granularity Difference</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Chemical APIs</td>
<td>271 - 化学药品原料药制造 (Chemical API Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Specific API category; ISIC: All drugs together</td>
</tr>
<tr class="even">
<td>Drug Formulations</td>
<td>272 - 化学药品制剂制造 (Chemical Preparation Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Separate formulation; ISIC: Combined</td>
</tr>
<tr class="odd">
<td>Biological Drugs</td>
<td>276 - 生物药品制造 (Biological Drug Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Dedicated biotech; ISIC: General pharma</td>
</tr>
<tr class="even">
<td>Vaccines</td>
<td>2761 - 疫苗制造 (Vaccine Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Specific vaccine category; ISIC: General</td>
</tr>
<tr class="odd">
<td>Blood Products</td>
<td>2762 - 血液制品制造 (Blood Product Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Specific blood products; ISIC: General</td>
</tr>
<tr class="even">
<td>Genetic Engineering</td>
<td>2763 - 基因工程药物和疫苗制造 ⭐ (Genetic Engineering Drug and Vaccine Manufacturing)</td>
<td>Division 21/Class 2100 - Manufacture of basic pharmaceutical products and pharmaceutical preparations</td>
<td>GB: Advanced biotech recognition; ISIC: General</td>
</tr>
<tr class="odd">
<td>Pharmaceutical Retail</td>
<td>No specific equivalent</td>
<td>4772 - Retail sale of pharmaceutical and medical goods, cosmetic and toilet articles</td>
<td>ISIC advantage: Retail coverage</td>
</tr>
</tbody>
</table>
<p><strong>Key Difference</strong>: GB provides 7 medium-level categories (271, 272, 276, 2761, 2762, 2763) vs ISIC’s single comprehensive Division 21.</p>
</section>
<section id="clean-technology-industry-cross-standard-comparison-清洁技术产业跨标准对比" class="level4">
<h4 class="anchored" data-anchor-id="clean-technology-industry-cross-standard-comparison-清洁技术产业跨标准对比">4. Clean Technology Industry Cross-Standard Comparison 清洁技术产业跨标准对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 23%">
<col style="width: 22%">
<col style="width: 16%">
<col style="width: 36%">
</colgroup>
<thead>
<tr class="header">
<th>Clean Technology</th>
<th>GB/T 4754-2017</th>
<th>ISIC Rev.5</th>
<th>Classification Innovation</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Solar Equipment</td>
<td>3862 - 太阳能器具制造 (Solar Equipment Manufacturing)</td>
<td>Class 2611 - Manufacture of solar cells, solar panels and photovoltaic inverters ⭐</td>
<td>Both have specific solar categories</td>
</tr>
<tr class="even">
<td>Solar Power Generation</td>
<td>4416 - 太阳能发电 (Solar Power Generation)</td>
<td>Class 3512 - Electric power generation activities from renewable sources ⭐</td>
<td>Both recognize renewable energy generation</td>
</tr>
<tr class="odd">
<td>Wind Equipment</td>
<td>3415 - 风能原动设备制造 (Wind Power Equipment Manufacturing)</td>
<td>Class 2811 - Manufacture of engines and turbines, except aircraft, vehicle and cycle engines</td>
<td>GB: Dedicated wind category; ISIC: General engines</td>
</tr>
<tr class="even">
<td>Wind Power Generation</td>
<td>4415 - 风力发电 (Wind Power Generation)</td>
<td>Class 3512 - Electric power generation activities from renewable sources ⭐</td>
<td>Both recognize renewable energy generation</td>
</tr>
<tr class="odd">
<td>Solar Cell Equipment</td>
<td>3562 - 半导体器件专用设备制造 (Semiconductor Device Equipment Manufacturing)</td>
<td>Class 2611 - Manufacture of solar cells, solar panels and photovoltaic inverters ⭐</td>
<td>Both cover solar cell manufacturing</td>
</tr>
<tr class="even">
<td>Biomass Energy</td>
<td>4417 - 生物质能发电 (Biomass Power Generation)</td>
<td>Class 3512 - Electric power generation activities from renewable sources ⭐</td>
<td>Both recognize biomass as renewable</td>
</tr>
<tr class="odd">
<td>Wind Engineering</td>
<td>4874 - 风能发电工程施工 (Wind Power Engineering Construction)</td>
<td>No specific equivalent</td>
<td>GB advantage: Engineering services</td>
</tr>
<tr class="even">
<td>Solar Engineering</td>
<td>4875 - 太阳能发电工程施工 (Solar Power Engineering Construction)</td>
<td>No specific equivalent</td>
<td>GB advantage: Engineering services</td>
</tr>
<tr class="odd">
<td>Technology Services</td>
<td>7515 - 新能源技术推广服务 (New Energy Technology Promotion Services)</td>
<td>No specific equivalent</td>
<td>GB advantage: Technology promotion services</td>
</tr>
</tbody>
</table>
<p><strong>Key Finding</strong>: Both standards recognize clean technology importance, with ISIC showing specific innovations in solar equipment (2611) and renewable energy generation (3512), while GB provides comprehensive value chain coverage including engineering and promotion services.</p>
</section>
<section id="cross-industry-pattern-analysis-跨产业模式分析" class="level4">
<h4 class="anchored" data-anchor-id="cross-industry-pattern-analysis-跨产业模式分析">5. Cross-Industry Pattern Analysis 跨产业模式分析</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 26%">
<col style="width: 27%">
<col style="width: 23%">
<col style="width: 23%">
</colgroup>
<thead>
<tr class="header">
<th>Classification Pattern</th>
<th>GB/T 4754-2017 Approach</th>
<th>ISIC Rev.5 Approach</th>
<th>Strategic Difference</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>Technology Separation</strong></td>
<td>Separates emerging from traditional (3611 vs 3612)</td>
<td>Groups technologies together (2910 all vehicles)</td>
<td>GB: Technology-driven; ISIC: Function-driven</td>
</tr>
<tr class="even">
<td><strong>Value Chain Coverage</strong></td>
<td>Manufacturing-focused with some services</td>
<td>Complete value chain (manufacturing + retail + wholesale)</td>
<td>GB: Production emphasis; ISIC: Market emphasis</td>
</tr>
<tr class="odd">
<td><strong>Innovation Recognition</strong></td>
<td>Explicit categories for new technologies</td>
<td>Strategic innovations in key areas (2611, 3512)</td>
<td>GB: Comprehensive; ISIC: Selective</td>
</tr>
<tr class="even">
<td><strong>Service Integration</strong></td>
<td>Technology promotion services (7515)</td>
<td>Professional services broadly categorized</td>
<td>GB: Technology-specific services; ISIC: General services</td>
</tr>
<tr class="odd">
<td><strong>Granularity Strategy</strong></td>
<td>Detailed hierarchy (3-4 levels)</td>
<td>Broader categories with strategic depth</td>
<td>GB: Detailed mapping; ISIC: Efficient grouping</td>
</tr>
</tbody>
</table>
</section>
<section id="emerging-technology-adaptation-comparison-新兴技术适应性对比" class="level4">
<h4 class="anchored" data-anchor-id="emerging-technology-adaptation-comparison-新兴技术适应性对比">6. Emerging Technology Adaptation Comparison 新兴技术适应性对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 22%">
<col style="width: 30%">
<col style="width: 24%">
<col style="width: 22%">
</colgroup>
<thead>
<tr class="header">
<th>Technology Trend</th>
<th>GB/T 4754-2017 Response</th>
<th>ISIC Rev.5 Response</th>
<th>Adaptation Speed</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>Electric Vehicles</strong></td>
<td>Dedicated category 3612 ⭐</td>
<td>Grouped with all vehicles</td>
<td>GB: Fast adaptation</td>
</tr>
<tr class="even">
<td><strong>Solar Technology</strong></td>
<td>Multiple categories (3862, 4416)</td>
<td>Specific category 2611 ⭐</td>
<td>Both: Good adaptation</td>
</tr>
<tr class="odd">
<td><strong>AI/Software</strong></td>
<td>Detailed software hierarchy</td>
<td>Broad programming category</td>
<td>GB: Better granularity</td>
</tr>
<tr class="even">
<td><strong>Biotechnology</strong></td>
<td>Genetic engineering category 2763 ⭐</td>
<td>General pharmaceutical category</td>
<td>GB: Advanced recognition</td>
</tr>
<tr class="odd">
<td><strong>Clean Energy Services</strong></td>
<td>Technology promotion 7515 ⭐</td>
<td>No specific equivalent</td>
<td>GB: Service innovation</td>
</tr>
</tbody>
</table>
</section>
<section id="statistical-and-policy-implications-统计和政策意义对比" class="level4">
<h4 class="anchored" data-anchor-id="statistical-and-policy-implications-统计和政策意义对比">7. Statistical and Policy Implications 统计和政策意义对比</h4>
<table class="caption-top table">
<colgroup>
<col style="width: 21%">
<col style="width: 31%">
<col style="width: 25%">
<col style="width: 21%">
</colgroup>
<thead>
<tr class="header">
<th>Application Area</th>
<th>GB/T 4754-2017 Advantage</th>
<th>ISIC Rev.5 Advantage</th>
<th>Optimal Use Case</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>Domestic Policy</strong></td>
<td>Precise targeting (NEV, clean tech)</td>
<td>International benchmarking</td>
<td>GB for domestic strategy</td>
</tr>
<tr class="even">
<td><strong>International Trade</strong></td>
<td>Technology export classification</td>
<td>Global trade compatibility</td>
<td>ISIC for trade analysis</td>
</tr>
<tr class="odd">
<td><strong>Investment Analysis</strong></td>
<td>Emerging sector identification</td>
<td>Cross-country comparison</td>
<td>GB for domestic investment</td>
</tr>
<tr class="even">
<td><strong>Statistical Reporting</strong></td>
<td>Technology trend tracking</td>
<td>International data exchange</td>
<td>Hybrid approach needed</td>
</tr>
<tr class="odd">
<td><strong>Research &amp; Development</strong></td>
<td>Innovation category mapping</td>
<td>Global R&amp;D comparison</td>
<td>GB for domestic R&amp;D policy</td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="comprehensive-key-findings-综合关键发现" class="level3">
<h3 class="anchored" data-anchor-id="comprehensive-key-findings-综合关键发现">Comprehensive Key Findings 综合关键发现</h3>
<section id="ai-industry-findings" class="level4">
<h4 class="anchored" data-anchor-id="ai-industry-findings">AI Industry Findings</h4>
<ul>
<li><strong>GB/T 4754-2017</strong>: Detailed 3-tier software development hierarchy (6511/6512/6513)</li>
<li><strong>ISIC Rev.5</strong>: Broader computer programming approach with Section K coverage</li>
<li><strong>Gap</strong>: Both lack specific machine learning and AI algorithm categories</li>
</ul>
</section>
<section id="automotive-industry-findings" class="level4">
<h4 class="anchored" data-anchor-id="automotive-industry-findings">Automotive Industry Findings</h4>
<ul>
<li><strong>GB Innovation</strong>: Revolutionary 3612 New Energy Vehicle separation ⭐</li>
<li><strong>ISIC Limitation</strong>: All vehicle types grouped under single Class 2910</li>
<li><strong>GB Advantage</strong>: EV infrastructure recognition (3840 charging stations)</li>
<li><strong>ISIC Advantage</strong>: Complete value chain coverage (manufacturing + sales)</li>
</ul>
</section>
<section id="biomedicine-industry-findings" class="level4">
<h4 class="anchored" data-anchor-id="biomedicine-industry-findings">Biomedicine Industry Findings</h4>
<ul>
<li><strong>GB Granularity</strong>: 7 medium-level categories vs ISIC’s single Division 21</li>
<li><strong>GB Innovation</strong>: Genetic engineering specific category (2763) ⭐</li>
<li><strong>ISIC Consolidation</strong>: All pharmaceuticals under one comprehensive category</li>
<li><strong>Trade-off</strong>: GB detail vs ISIC international comparability</li>
</ul>
</section>
<section id="clean-technology-industry-findings" class="level4">
<h4 class="anchored" data-anchor-id="clean-technology-industry-findings">Clean Technology Industry Findings</h4>
<ul>
<li><strong>ISIC Innovation</strong>: Specific solar equipment (2611) and renewable energy (3512) ⭐</li>
<li><strong>GB Comprehensive</strong>: Complete value chain (equipment + generation + services)</li>
<li><strong>Both Standards</strong>: Strong recognition of clean technology importance</li>
<li><strong>GB Unique</strong>: Technology promotion services (7515) not found in ISIC</li>
</ul>
</section>
<section id="cross-industry-patterns" class="level4">
<h4 class="anchored" data-anchor-id="cross-industry-patterns">Cross-Industry Patterns</h4>
<ul>
<li><strong>GB Strategy</strong>: Technology-driven separation and detailed hierarchies</li>
<li><strong>ISIC Strategy</strong>: Function-driven grouping with strategic innovations</li>
<li><strong>Emerging Tech</strong>: GB faster adaptation, ISIC selective but effective</li>
<li><strong>Policy Impact</strong>: GB better for domestic targeting, ISIC for international comparison</li>
</ul>
<hr>
</section>
</section>
</section>
<section id="overall-conclusions-总体结论" class="level2">
<h2 class="anchored" data-anchor-id="overall-conclusions-总体结论">Overall Conclusions 总体结论</h2>
<section id="classification-granularity-分类粒度" class="level3">
<h3 class="anchored" data-anchor-id="classification-granularity-分类粒度">Classification Granularity 分类粒度</h3>
<ol type="1">
<li><strong>GB/T 4754-2017</strong> consistently provides more detailed and specific classifications</li>
<li><strong>ISIC Rev.5</strong> uses broader categories but has specific innovations (solar equipment 2611, renewable energy 3512)</li>
<li>Both standards show adaptation to emerging technologies, with different approaches</li>
</ol>
</section>
<section id="industry-coverage-产业覆盖" class="level3">
<h3 class="anchored" data-anchor-id="industry-coverage-产业覆盖">Industry Coverage 产业覆盖</h3>
<ol type="1">
<li>Both standards cover the four analyzed industries with complementary strengths</li>
<li><strong>GB advantage</strong>: New energy vehicle separation (3612), technology promotion services (7515)</li>
<li><strong>ISIC innovation</strong>: Specific solar equipment manufacturing (2611), renewable energy generation (3512)</li>
<li><strong>Convergence</strong>: Both recognize the importance of clean technology and renewable energy</li>
</ol>
</section>
<section id="practical-implications-实际意义" class="level3">
<h3 class="anchored" data-anchor-id="practical-implications-实际意义">Practical Implications 实际意义</h3>
<ol type="1">
<li><strong>For Policy Making:</strong> GB standard provides better granularity for targeted industry policies</li>
<li><strong>For International Comparison:</strong> ISIC standard offers better comparability across countries</li>
<li><strong>For Statistical Analysis:</strong> Choice of standard significantly impacts industry analysis results</li>
</ol>
</section>
<section id="recommendations-建议" class="level3">
<h3 class="anchored" data-anchor-id="recommendations-建议">Recommendations 建议</h3>
<ol type="1">
<li><strong>Harmonization Efforts:</strong> Develop mapping tables between GB and ISIC classifications</li>
<li><strong>Regular Updates:</strong> Both standards should be updated to reflect technological advances</li>
<li><strong>Hybrid Approach:</strong> Consider using GB for domestic analysis and ISIC for international comparison</li>
</ol>
<hr>
</section>
</section>
<section id="technical-notes-技术说明" class="level2">
<h2 class="anchored" data-anchor-id="technical-notes-技术说明">Technical Notes 技术说明</h2>
<section id="methodology-方法论" class="level3">
<h3 class="anchored" data-anchor-id="methodology-方法论">Methodology 方法论</h3>
<ul>
<li><strong>Keyword Matching:</strong> Fuzzy matching algorithm with 0.3 minimum threshold</li>
<li><strong>Hierarchy Analysis:</strong> Four-level classification comparison</li>
<li><strong>Scoring System:</strong> Match scores based on keyword coverage and relevance</li>
</ul>
</section>
<section id="data-sources-数据来源" class="level3">
<h3 class="anchored" data-anchor-id="data-sources-数据来源">Data Sources 数据来源</h3>
<ul>
<li><strong>GB/T 4754-2017:</strong> final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv</li>
<li><strong>ISIC Rev.5:</strong> final_data/ISIC_Rev5/ISIC_Rev5_en.csv</li>
</ul>
</section>
<section id="tool-information-工具信息" class="level3">
<h3 class="anchored" data-anchor-id="tool-information-工具信息">Tool Information 工具信息</h3>
<ul>
<li><strong>Analysis Tool:</strong> IndustryMatcher class</li>
<li><strong>Programming Language:</strong> Python 3.x</li>
<li><strong>Key Libraries:</strong> pandas, numpy, plotly</li>
</ul>
<hr>
<p><em>Report generated by Industry Comparison Analysis Tool</em><br>
<em>产业比较分析工具生成报告</em></p>
</section>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>