<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>industry_classification_logic_analysis</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
</style>


<script src="Industry_Classification_Logic_Analysis_files/libs/clipboard/clipboard.min.js"></script>
<script src="Industry_Classification_Logic_Analysis_files/libs/quarto-html/quarto.js"></script>
<script src="Industry_Classification_Logic_Analysis_files/libs/quarto-html/popper.min.js"></script>
<script src="Industry_Classification_Logic_Analysis_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="Industry_Classification_Logic_Analysis_files/libs/quarto-html/anchor.min.js"></script>
<link href="Industry_Classification_Logic_Analysis_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="Industry_Classification_Logic_Analysis_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="Industry_Classification_Logic_Analysis_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="Industry_Classification_Logic_Analysis_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="Industry_Classification_Logic_Analysis_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content"><header id="title-block-header" class="quarto-title-block"></header>




<section id="industry-classification-logic-and-criteria-analysis" class="level1">
<h1>Industry Classification Logic and Criteria Analysis</h1>
</section>
<section id="产业分类逻辑和依据分析" class="level1">
<h1>产业分类逻辑和依据分析</h1>
<p><strong>Analysis Date:</strong> January 2025<br>
<strong>Scope:</strong> Four Key Industries Classification Logic in GB/T 4754-2017 vs ISIC Rev.5</p>
<hr>
<section id="ai-artificial-intelligence-industry-classification-logic" class="level2">
<h2 class="anchored" data-anchor-id="ai-artificial-intelligence-industry-classification-logic">1. AI Artificial Intelligence Industry Classification Logic</h2>
</section>
<section id="ai人工智能产业分类逻辑" class="level2">
<h2 class="anchored" data-anchor-id="ai人工智能产业分类逻辑">AI人工智能产业分类逻辑</h2>
<section id="classification-criteria-and-rationale-分类标准和依据" class="level3">
<h3 class="anchored" data-anchor-id="classification-criteria-and-rationale-分类标准和依据">Classification Criteria and Rationale 分类标准和依据</h3>
<section id="why-these-industries-belong-to-ai-sector-为什么这些行业属于ai产业" class="level4">
<h4 class="anchored" data-anchor-id="why-these-industries-belong-to-ai-sector-为什么这些行业属于ai产业">Why These Industries Belong to AI Sector 为什么这些行业属于AI产业</h4>
<p><strong>GB/T 4754-2017 AI Classifications:</strong></p>
<p><strong>1. Software Development Hierarchy (软件开发层级) - Division 651</strong> - <strong>Basic Software Development (基础软件开发) - Code 6511</strong> - Logic: AI infrastructure and operating systems - Criteria: AI frameworks, development platforms, system software - Justification: Foundation layer for AI applications</p>
<ul>
<li><strong>Support Software Development (支撑软件开发) - Code 6512</strong>
<ul>
<li>Logic: AI development tools and middleware</li>
<li>Criteria: AI development environments, debugging tools, optimization software</li>
<li>Justification: Essential tools for AI development</li>
</ul></li>
<li><strong>Application Software Development (应用软件开发) - Code 6513</strong>
<ul>
<li>Logic: End-user AI applications</li>
<li>Criteria: AI-powered applications, intelligent software solutions</li>
<li>Justification: Direct AI product delivery to users</li>
</ul></li>
</ul>
<p><strong>2. Integrated Circuit Design (集成电路设计) - Code 6520</strong> - <strong>Logic:</strong> AI requires specialized chips (AI chips, GPUs, TPUs) - <strong>Criteria:</strong> AI accelerator design, neural processing units - <strong>Justification:</strong> Hardware foundation for AI computing</p>
<p><strong>3. Information Processing and Storage Support Services (信息处理和存储支持服务) - Code 6550</strong> - <strong>Logic:</strong> AI requires massive data processing and storage - <strong>Criteria:</strong> Big data analytics, cloud AI services, data mining - <strong>Justification:</strong> Data is the fuel of AI systems</p>
<p><strong>ISIC Rev.5 AI Classifications:</strong></p>
<p><strong>1. Computer Programming Activities - Code 6201</strong> - <strong>Logic:</strong> AI development requires specialized programming - <strong>Criteria:</strong> Neural network programming, ML framework development - <strong>Justification:</strong> AI is implemented through computer programming</p>
<p><strong>2. Manufacture of Electronic Components - Code 2610</strong> - <strong>Logic:</strong> AI hardware components manufacturing - <strong>Criteria:</strong> AI chips, processors, specialized hardware - <strong>Justification:</strong> Physical infrastructure for AI systems</p>
</section>
</section>
<section id="classification-gaps-and-missing-categories-分类空白和缺失类别" class="level3">
<h3 class="anchored" data-anchor-id="classification-gaps-and-missing-categories-分类空白和缺失类别">Classification Gaps and Missing Categories 分类空白和缺失类别</h3>
<p><strong>Missing in Both Standards:</strong> - AI chip design and manufacturing - AI platform services - Machine learning as a service (MLaaS) - Computer vision specialized services - Natural language processing services</p>
<hr>
</section>
</section>
<section id="automotive-industry-classification-logic" class="level2">
<h2 class="anchored" data-anchor-id="automotive-industry-classification-logic">2. Automotive Industry Classification Logic</h2>
</section>
<section id="汽车产业分类逻辑" class="level2">
<h2 class="anchored" data-anchor-id="汽车产业分类逻辑">汽车产业分类逻辑</h2>
<section id="classification-criteria-and-rationale-分类标准和依据-1" class="level3">
<h3 class="anchored" data-anchor-id="classification-criteria-and-rationale-分类标准和依据-1">Classification Criteria and Rationale 分类标准和依据</h3>
<section id="why-these-industries-belong-to-automotive-sector-为什么这些行业属于汽车产业" class="level4">
<h4 class="anchored" data-anchor-id="why-these-industries-belong-to-automotive-sector-为什么这些行业属于汽车产业">Why These Industries Belong to Automotive Sector 为什么这些行业属于汽车产业</h4>
<p><strong>GB/T 4754-2017 Automotive Classifications:</strong></p>
<p><strong>1. Gasoline and Diesel Vehicle Manufacturing (汽柴油车整车制造) - Code 3611</strong> - <strong>Logic:</strong> Traditional internal combustion engine vehicles - <strong>Criteria:</strong> Gasoline engines, diesel engines, conventional powertrains - <strong>Justification:</strong> Established automotive technology</p>
<p><strong>2. New Energy Vehicle Manufacturing (新能源车整车制造) - Code 3612</strong> ⭐ - <strong>Logic:</strong> Revolutionary separation of NEV from traditional vehicles - <strong>Criteria:</strong> Electric vehicles, hybrid vehicles, fuel cell vehicles - <strong>Justification:</strong> Recognition of technology transformation in automotive industry - <strong>Innovation:</strong> First classification standard to explicitly separate NEV</p>
<p><strong>3. Auto Parts and Accessories Manufacturing (汽车零部件及配件制造) - Code 3670</strong> - <strong>Logic:</strong> Supporting components for all vehicle types - <strong>Criteria:</strong> Engine components, electronic systems, autonomous driving sensors - <strong>Justification:</strong> Complete automotive supply chain coverage</p>
<p><strong>4. Charging Station Manufacturing (充电桩制造) - Code 3840</strong> - <strong>Logic:</strong> Essential NEV infrastructure - <strong>Criteria:</strong> EV charging equipment, smart charging systems - <strong>Justification:</strong> Critical NEV ecosystem component</p>
<p><strong>ISIC Rev.5 Automotive Classifications:</strong></p>
<p><strong>1. Manufacture of Motor Vehicles - Code 2910</strong> - <strong>Logic:</strong> All vehicle types in single category - <strong>Criteria:</strong> Gasoline, diesel, electric, hybrid vehicles combined - <strong>Limitation:</strong> No distinction between traditional and new energy vehicles</p>
<p><strong>2. Sale of Motor Vehicles - Code 4510</strong> - <strong>Logic:</strong> Vehicle retail and distribution - <strong>Criteria:</strong> All vehicle sales activities - <strong>Coverage:</strong> Complete value chain approach</p>
</section>
</section>
<section id="new-energy-vehicle-specific-classifications-新能源汽车特定分类" class="level3">
<h3 class="anchored" data-anchor-id="new-energy-vehicle-specific-classifications-新能源汽车特定分类">New Energy Vehicle Specific Classifications 新能源汽车特定分类</h3>
<p><strong>GB/T 4754-2017 Advantages:</strong> - Specific category for charging infrastructure - Battery manufacturing classifications - Electric motor production categories</p>
<p><strong>ISIC Rev.5 Limitations:</strong> - Groups all motor vehicles together - No distinction for new energy vehicles - Limited coverage of EV-specific components</p>
</section>
<section id="autonomous-driving-technology-classification-自动驾驶技术分类" class="level3">
<h3 class="anchored" data-anchor-id="autonomous-driving-technology-classification-自动驾驶技术分类">Autonomous Driving Technology Classification 自动驾驶技术分类</h3>
<p><strong>Current Classification Challenges:</strong> - Sensors (LiDAR, cameras) scattered across different categories - Software components classified under general software - Integration systems lack specific classification</p>
<p><strong>Recommended Classification Logic:</strong> - Hardware: Sensor manufacturing - Software: Autonomous driving algorithms - Services: Vehicle-to-everything (V2X) communication</p>
<hr>
</section>
</section>
<section id="biomedicine-industry-classification-logic" class="level2">
<h2 class="anchored" data-anchor-id="biomedicine-industry-classification-logic">3. Biomedicine Industry Classification Logic</h2>
</section>
<section id="生物医药产业分类逻辑" class="level2">
<h2 class="anchored" data-anchor-id="生物医药产业分类逻辑">生物医药产业分类逻辑</h2>
<section id="classification-criteria-and-rationale-分类标准和依据-2" class="level3">
<h3 class="anchored" data-anchor-id="classification-criteria-and-rationale-分类标准和依据-2">Classification Criteria and Rationale 分类标准和依据</h3>
<section id="why-these-industries-belong-to-biomedicine-sector-为什么这些行业属于生物医药产业" class="level4">
<h4 class="anchored" data-anchor-id="why-these-industries-belong-to-biomedicine-sector-为什么这些行业属于生物医药产业">Why These Industries Belong to Biomedicine Sector 为什么这些行业属于生物医药产业</h4>
<p><strong>1. Pharmaceutical Manufacturing (医药制造) - GB Major Category 27</strong> - <strong>Logic:</strong> Core biomedicine production activities - <strong>Criteria:</strong> - Drug development and production - Biological product manufacturing - Medical device production - <strong>Evidence:</strong> Direct industry classification - <strong>Justification:</strong> Primary biomedicine sector</p>
<p><strong>2. Vaccine Manufacturing (疫苗制造) - GB Code 2761</strong> - <strong>Logic:</strong> Specialized biological product - <strong>Criteria:</strong> - Biological manufacturing processes - Regulatory compliance requirements - Public health importance - <strong>Evidence:</strong> Keywords match “疫苗”, “生物制品” - <strong>Justification:</strong> Critical biomedicine subsector</p>
<p><strong>3. Genetic Engineering Products (基因工程药物) - GB Code 2763</strong> - <strong>Logic:</strong> Advanced biotechnology application - <strong>Criteria:</strong> - Recombinant DNA technology - Protein engineering - Gene therapy products - <strong>Evidence:</strong> Keywords match “基因工程”, “生物技术” - <strong>Justification:</strong> Cutting-edge biomedicine technology</p>
</section>
</section>
<section id="classification-hierarchy-logic-分类层级逻辑" class="level3">
<h3 class="anchored" data-anchor-id="classification-hierarchy-logic-分类层级逻辑">Classification Hierarchy Logic 分类层级逻辑</h3>
<p><strong>GB/T 4754-2017 Detailed Approach:</strong> - Separates chemical drugs from biological drugs - Distinguishes traditional Chinese medicine - Provides specific categories for advanced biotechnology</p>
<p><strong>ISIC Rev.5 Broad Approach:</strong> - Groups all pharmaceutical activities together - No distinction between drug types - Limited recognition of biotechnology advances</p>
</section>
<section id="medical-device-classification-logic-医疗器械分类逻辑" class="level3">
<h3 class="anchored" data-anchor-id="medical-device-classification-logic-医疗器械分类逻辑">Medical Device Classification Logic 医疗器械分类逻辑</h3>
<p><strong>Classification Criteria:</strong> - Diagnostic equipment: Based on imaging technology - Therapeutic equipment: Based on treatment method - Surgical instruments: Based on surgical specialty - In vitro diagnostics: Based on testing methodology</p>
<hr>
</section>
</section>
<section id="clean-technology-industry-classification-logic" class="level2">
<h2 class="anchored" data-anchor-id="clean-technology-industry-classification-logic">4. Clean Technology Industry Classification Logic</h2>
</section>
<section id="清洁技术产业分类逻辑" class="level2">
<h2 class="anchored" data-anchor-id="清洁技术产业分类逻辑">清洁技术产业分类逻辑</h2>
<section id="classification-criteria-and-rationale-分类标准和依据-3" class="level3">
<h3 class="anchored" data-anchor-id="classification-criteria-and-rationale-分类标准和依据-3">Classification Criteria and Rationale 分类标准和依据</h3>
<section id="why-these-industries-belong-to-clean-technology-sector-为什么这些行业属于清洁技术产业" class="level4">
<h4 class="anchored" data-anchor-id="why-these-industries-belong-to-clean-technology-sector-为什么这些行业属于清洁技术产业">Why These Industries Belong to Clean Technology Sector 为什么这些行业属于清洁技术产业</h4>
<p><strong>GB/T 4754-2017 Clean Technology Classifications:</strong></p>
<p><strong>1. Wind Power Equipment Manufacturing (风能原动设备制造) - Code 3415</strong> - <strong>Logic:</strong> Wind energy conversion technology - <strong>Criteria:</strong> Wind turbines, wind power generation systems, grid integration - <strong>Justification:</strong> Major renewable energy technology</p>
<p><strong>2. Solar Equipment Manufacturing (太阳能器具制造) - Code 3862</strong> - <strong>Logic:</strong> Solar energy conversion technology - <strong>Criteria:</strong> Solar panels, solar collectors, photovoltaic systems - <strong>Justification:</strong> Core renewable energy technology</p>
<p><strong>3. Wind Power Generation (风力发电) - Code 4415</strong> - <strong>Logic:</strong> Renewable energy power generation - <strong>Criteria:</strong> Wind farm operation, wind electricity generation - <strong>Justification:</strong> Clean energy production</p>
<p><strong>4. Solar Power Generation (太阳能发电) - Code 4416</strong> - <strong>Logic:</strong> Solar energy power generation - <strong>Criteria:</strong> Solar farm operation, photovoltaic electricity generation - <strong>Justification:</strong> Clean energy production</p>
<p><strong>5. New Energy Technology Promotion Services (新能源技术推广服务) - Code 7515</strong> - <strong>Logic:</strong> Technology transfer and promotion services - <strong>Criteria:</strong> Clean technology consulting, promotion, implementation - <strong>Justification:</strong> Supporting clean technology adoption</p>
<p><strong>ISIC Rev.5 Clean Technology Classifications:</strong></p>
<p><strong>1. Manufacture of Solar Cells, Solar Panels and Photovoltaic Inverters - Code 2611</strong> ⭐ - <strong>Logic:</strong> Specific solar equipment manufacturing - <strong>Criteria:</strong> Solar cells, solar panels, photovoltaic inverters - <strong>Innovation:</strong> ISIC has dedicated solar equipment category</p>
<p><strong>2. Electric Power Generation Activities from Renewable Sources - Code 3512</strong> ⭐ - <strong>Logic:</strong> Renewable energy power generation - <strong>Criteria:</strong> Solar, wind, biomass, hydroelectric power generation - <strong>Innovation:</strong> ISIC separates renewable from non-renewable power</p>
<p><strong>3. Manufacture of Engines and Turbines - Code 2811</strong> - <strong>Logic:</strong> Includes wind turbines - <strong>Criteria:</strong> Wind turbines, other engines and turbines - <strong>Limitation:</strong> Wind turbines grouped with general machinery</p>
</section>
</section>
<section id="technology-specific-classification-logic-技术特定分类逻辑" class="level3">
<h3 class="anchored" data-anchor-id="technology-specific-classification-logic-技术特定分类逻辑">Technology-Specific Classification Logic 技术特定分类逻辑</h3>
<p><strong>Solar Technology Classification:</strong> - Manufacturing: Solar panel and component production - Installation: Solar system installation services - Operation: Solar power generation</p>
<p><strong>Wind Technology Classification:</strong> - Manufacturing: Wind turbine and component production - Development: Wind farm development - Operation: Wind power generation</p>
<p><strong>Energy Storage Classification:</strong> - Manufacturing: Battery and storage system production - Integration: Grid integration services - Management: Energy management systems</p>
</section>
<section id="environmental-technology-integration-环境技术整合" class="level3">
<h3 class="anchored" data-anchor-id="environmental-technology-integration-环境技术整合">Environmental Technology Integration 环境技术整合</h3>
<p><strong>Pollution Control Technologies:</strong> - Air pollution control equipment - Water treatment systems - Waste management technologies - Soil remediation equipment</p>
<p><strong>Energy Efficiency Technologies:</strong> - Building energy management systems - Industrial energy optimization - Transportation efficiency improvements</p>
<hr>
</section>
</section>
<section id="cross-industry-analysis-跨产业分析" class="level2">
<h2 class="anchored" data-anchor-id="cross-industry-analysis-跨产业分析">Cross-Industry Analysis 跨产业分析</h2>
<section id="technology-convergence-areas-技术融合领域" class="level3">
<h3 class="anchored" data-anchor-id="technology-convergence-areas-技术融合领域">Technology Convergence Areas 技术融合领域</h3>
<p><strong>1. AI + Automotive = Autonomous Vehicles</strong> - Classification Challenge: Spans both AI and automotive sectors - Current Approach: Classified primarily under automotive - Recommendation: Create hybrid classification category</p>
<p><strong>2. AI + Biomedicine = Digital Health</strong> - Classification Challenge: AI-powered medical devices and diagnostics - Current Approach: Classified under medical devices - Recommendation: Recognize AI component in medical classification</p>
<p><strong>3. AI + Clean Technology = Smart Grid</strong> - Classification Challenge: AI-optimized energy systems - Current Approach: Classified under energy equipment - Recommendation: Include smart technology subcategories</p>
</section>
<section id="emerging-technology-classification-challenges-新兴技术分类挑战" class="level3">
<h3 class="anchored" data-anchor-id="emerging-technology-classification-challenges-新兴技术分类挑战">Emerging Technology Classification Challenges 新兴技术分类挑战</h3>
<p><strong>1. Internet of Things (IoT)</strong> - Spans multiple industries - Requires cross-sector classification approach - Current standards lack specific IoT categories</p>
<p><strong>2. Blockchain Technology</strong> - Applicable across all four industries - No specific classification in either standard - Needs technology-agnostic classification framework</p>
<p><strong>3. Quantum Computing</strong> - Emerging technology with broad applications - Not covered in current classification systems - Requires forward-looking classification approach</p>
<hr>
</section>
</section>
<section id="recommendations-for-classification-improvement-分类改进建议" class="level2">
<h2 class="anchored" data-anchor-id="recommendations-for-classification-improvement-分类改进建议">Recommendations for Classification Improvement 分类改进建议</h2>
<section id="technology-agnostic-categories-技术无关类别" class="level3">
<h3 class="anchored" data-anchor-id="technology-agnostic-categories-技术无关类别">1. Technology-Agnostic Categories 技术无关类别</h3>
<ul>
<li>Create categories based on technology application rather than industry</li>
<li>Develop cross-reference systems between technology and industry classifications</li>
<li>Implement dynamic classification updates for emerging technologies</li>
</ul>
</section>
<section id="hybrid-classification-approach-混合分类方法" class="level3">
<h3 class="anchored" data-anchor-id="hybrid-classification-approach-混合分类方法">2. Hybrid Classification Approach 混合分类方法</h3>
<ul>
<li>Primary classification by traditional industry</li>
<li>Secondary classification by technology type</li>
<li>Tertiary classification by application domain</li>
</ul>
</section>
<section id="regular-update-mechanisms-定期更新机制" class="level3">
<h3 class="anchored" data-anchor-id="regular-update-mechanisms-定期更新机制">3. Regular Update Mechanisms 定期更新机制</h3>
<ul>
<li>Annual review of emerging technologies</li>
<li>Stakeholder consultation for classification updates</li>
<li>International coordination for classification harmonization</li>
</ul>
</section>
<section id="digital-classification-framework-数字分类框架" class="level3">
<h3 class="anchored" data-anchor-id="digital-classification-framework-数字分类框架">4. Digital Classification Framework 数字分类框架</h3>
<ul>
<li>Machine-readable classification codes</li>
<li>Automated classification suggestion systems</li>
<li>AI-powered classification validation</li>
</ul>
<hr>
<p><em>Analysis conducted using IndustryMatcher algorithm and expert domain knowledge</em><br>
<em>使用IndustryMatcher算法和专家领域知识进行分析</em></p>
</section>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>