# Project Structure - 项目结构说明

## Overview - 项目概述

This project contains interactive tree visualizations for two major industry classification standards:
- **ISIC Rev.5**: International Standard Industrial Classification (国际标准行业分类)
- **GB/T 4754-2017**: China's National Industry Classification Standard (中国国民经济行业分类标准)

本项目包含两个主要行业分类标准的交互式树形可视化工具。

## Main Visualization Files - 主要可视化文件

### Core Applications - 核心应用
- `isic_rev5_tree_visualization.html` - ISIC Rev.5 interactive tree visualization
- `gb_t_4754_tree_visualization.html` - GB/T 4754-2017 interactive tree visualization

### Data Files - 数据文件
- `isic_rev5_data_fixed_最终修正版数据.js` - Final corrected ISIC Rev.5 data
- `gb_t_4754_data_corrected_最终修正版数据.js` - Final corrected GB/T 4754-2017 data

## Documentation - 文档说明

### Project Documentation - 项目文档
- `ISIC_README.md` - ISIC Rev.5 project documentation
- `README_GB_T_4754.md` - GB/T 4754-2017 project documentation

### Data Correction Reports - 数据修正报告
- `CSV_Encoding_Error_Check_Report_数据质量检查报告.md` - CSV encoding error check report
- `GB_T_4754_2017_Data_Correction_Manual_数据修正操作指南.md` - GB/T 4754-2017 data correction manual
- `ISIC_Rev5_Data_Correction_Report_数据修正详细报告.md` - ISIC Rev.5 detailed correction report
- `Raw_CSV_Data_Correction_Guide_原始数据修正指南.md` - Raw CSV data correction guide
- `ISIC_Rev5_Correction_Report.html` - HTML version of ISIC correction report

## Tools and Scripts - 工具和脚本

### Analysis Tools - 分析工具
- `analyze_isic_structure_ISIC结构分析工具.py` - ISIC structure analysis tool
- `classification_analysis_分类体系分析工具.py` - Classification system analysis tool
- `classification_analysis_report.html` - Analysis report output

### Data Processing Tools - 数据处理工具
- `check_csv_encoding_errors_CSV编码检查工具.py` - CSV encoding error checker
- `generate_correction_report_数据修正报告生成器.py` - Data correction report generator
- `fix_isic_csv.py` - ISIC CSV data fixer

### Data Conversion Tools - 数据转换工具
- `csv_to_js.py` - Basic CSV to JavaScript converter
- `csv_to_js_compact.py` - Compact CSV to JavaScript converter
- `csv_to_js_converter.py` - Advanced CSV to JavaScript converter

### Testing and Debug Files - 测试和调试文件
- `debug_isic_ISIC数据调试页面.html` - ISIC data debugging page
- `test_isic_tree_ISIC树形结构测试页面.html` - ISIC tree structure test page

## Data Files - 数据文件

### Source Data - 源数据
- `GB_T_4754_2017_zh_en_corrected.csv` - Corrected GB/T 4754-2017 bilingual data
- `GB_T_4754_2017_zh_en_corrected.csv.bak` - Backup of corrected data
- `水的生产和供应业_修正示例.csv` - Water supply industry correction example

### JavaScript Data Files - JavaScript数据文件
- `gb_t_4754_data.js` - Original GB/T 4754-2017 data
- `gb_t_4754_data_compact.js` - Compact version
- `isic_rev5_data_compact.js` - Compact ISIC Rev.5 data
- `isic_rev5_data_complete.js` - Complete ISIC Rev.5 data
- `isic_rev5_data_corrected.js` - Corrected ISIC Rev.5 data
- `isic_rev5_data_final.js` - Final ISIC Rev.5 data

## Directory Structure - 目录结构

### `/raw_data/` - 原始数据目录
Contains original source files and extracted data from official documents.
包含官方文档的原始源文件和提取的数据。

### `/scripts/` - 脚本目录
Contains various data processing and translation scripts.
包含各种数据处理和翻译脚本。

### `/documentation/` - 文档目录
Contains workflow documentation and extraction process guides.
包含工作流程文档和提取过程指南。

### `/final_data/` - 最终数据目录
Contains the final processed and verified data files.
包含最终处理和验证的数据文件。

### `/archive/` - 归档目录
Contains backup files and version history.
包含备份文件和版本历史。

## Usage Instructions - 使用说明

1. **For ISIC Rev.5**: Open `isic_rev5_tree_visualization.html` in a web browser
2. **For GB/T 4754-2017**: Open `gb_t_4754_tree_visualization.html` in a web browser
3. **For data analysis**: Use the Python tools in the root directory
4. **For documentation**: Refer to the respective README files

## File Naming Convention - 文件命名规范

- **Bilingual naming**: Important files include both English and Chinese descriptions
- **Descriptive suffixes**: Files include functional descriptions in their names
- **Version indicators**: Data files include version/status indicators (e.g., "最终修正版")

---

*Last updated: January 2025*
