#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Industry Comparison Analysis Tool - 产业比较分析工具

This tool provides comprehensive comparison analysis between GB/T 4754-2017 
and ISIC Rev.5 classification standards for four key industries:
- AI Artificial Intelligence (AI人工智能)
- Automotive (自动驾驶/新能源汽车)
- Biomedicine (生物医药)
- Clean Technology (清洁技术)

Author: AI Assistant
Created: January 2025
Version: 1.0
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo
import json
import argparse
import sys
import os
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

# Import our custom modules
from industry_keywords_产业关键词库 import IndustryKeywords, IndustryType


class DataLoader:
    """
    Data loading and preprocessing module
    数据读取和预处理模块
    """
    
    def __init__(self, base_path: str = ".."):
        """
        Initialize DataLoader with base path
        
        Args:
            base_path: Base path to the project directory
        """
        self.base_path = Path(base_path)
        self.gb_data = None
        self.isic_data = None
        
    def load_gb_data(self) -> pd.DataFrame:
        """
        Load GB/T 4754-2017 classification data
        加载GB/T 4754-2017分类数据
        
        Returns:
            DataFrame containing GB classification data
        """
        try:
            gb_file_path = self.base_path / "final_data" / "GB_T_4754_2017" / "GB_T_4754_2017_zh_en.csv"
            
            print(f"Loading GB/T 4754-2017 data from: {gb_file_path}")
            
            # Read the CSV file
            gb_df = pd.read_csv(gb_file_path, encoding='utf-8')
            
            # Check actual column names in the file
            print(f"Actual GB columns: {list(gb_df.columns)}")

            # Map Chinese column names to English equivalents
            column_mapping = {
                '分类级别': 'Category_level_CN',
                'Category_level': 'Category_level',
                '分类代码': 'Code_CN',
                'Code': 'Code',
                '分类名称': 'Name_CN',
                'Name': 'Name',
                '上级代码': 'Parent_code_CN',
                'Parent_code': 'Parent_code',
                '核心关键词': 'Core_keyword_CN',
                'Core_keyword': 'Core_keyword',
                '说明': 'Note_CN',
                'Note': 'Note'
            }

            # Rename columns for consistency
            gb_df = gb_df.rename(columns=column_mapping)

            # Validate required columns (use available columns)
            required_columns = ['Category_level', 'Code', 'Name']
            missing_columns = [col for col in required_columns if col not in gb_df.columns]
            
            if missing_columns:
                print(f"Warning: Missing columns in GB data: {missing_columns}")
                print(f"Available columns: {list(gb_df.columns)}")
            
            # Clean and standardize data
            gb_df = self._clean_gb_data(gb_df)
            
            self.gb_data = gb_df
            print(f"Successfully loaded {len(gb_df)} GB/T 4754-2017 records")
            
            return gb_df
            
        except Exception as e:
            print(f"Error loading GB/T 4754-2017 data: {str(e)}")
            raise
    
    def load_isic_data(self) -> pd.DataFrame:
        """
        Load ISIC Rev.5 classification data
        加载ISIC Rev.5分类数据
        
        Returns:
            DataFrame containing ISIC classification data
        """
        try:
            isic_file_path = self.base_path / "final_data" / "ISIC_Rev5" / "ISIC_Rev5_en.csv"
            
            print(f"Loading ISIC Rev.5 data from: {isic_file_path}")
            
            # Read the CSV file
            isic_df = pd.read_csv(isic_file_path, encoding='utf-8')
            
            # Check actual column names in the file
            print(f"Actual ISIC columns: {list(isic_df.columns)}")

            # Validate required columns (use available columns)
            required_columns = ['Classification_level', 'Code', 'Name']
            missing_columns = [col for col in required_columns if col not in isic_df.columns]
            
            if missing_columns:
                print(f"Warning: Missing columns in ISIC data: {missing_columns}")
                print(f"Available columns: {list(isic_df.columns)}")
            
            # Clean and standardize data
            isic_df = self._clean_isic_data(isic_df)
            
            self.isic_data = isic_df
            print(f"Successfully loaded {len(isic_df)} ISIC Rev.5 records")
            
            return isic_df
            
        except Exception as e:
            print(f"Error loading ISIC Rev.5 data: {str(e)}")
            raise
    
    def _clean_gb_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize GB/T 4754-2017 data
        清洗和标准化GB/T 4754-2017数据
        """
        # Remove rows with missing essential data
        df = df.dropna(subset=['Code', 'Name'])
        
        # Standardize level names
        level_mapping = {
            '门类': 'Category',
            '大类': 'Major', 
            '中类': 'Medium',
            '小类': 'Subclass'
        }
        
        if 'Category_level' in df.columns:
            df['Level_Standard'] = df['Category_level'].map(level_mapping).fillna(df['Category_level'])
        
        # Clean code format
        df['Code'] = df['Code'].astype(str).str.strip()
        
        # Clean names
        df['Name'] = df['Name'].astype(str).str.strip()
        df['Name_EN'] = df.get('Name', '')  # English name if available
        
        # Handle keywords
        if 'Core_keyword' in df.columns:
            df['Keywords'] = df['Core_keyword'].fillna('')
        else:
            df['Keywords'] = ''
        
        return df
    
    def _clean_isic_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize ISIC Rev.5 data
        清洗和标准化ISIC Rev.5数据
        """
        # Remove rows with missing essential data
        df = df.dropna(subset=['Code', 'Name'])
        
        # Standardize level names
        level_mapping = {
            'Section': 'Section',
            'Division': 'Division',
            'Group': 'Group',
            'Class': 'Class'
        }
        
        if 'Classification_level' in df.columns:
            df['Level_Standard'] = df['Classification_level'].map(level_mapping).fillna(df['Classification_level'])
        
        # Clean code format
        df['Code'] = df['Code'].astype(str).str.strip()
        
        # Clean names
        df['Name'] = df['Name'].astype(str).str.strip()
        
        # Handle keywords
        if 'Core_keyword' in df.columns:
            df['Keywords'] = df['Core_keyword'].fillna('')
        else:
            df['Keywords'] = ''
        
        return df
    
    def validate_data(self) -> Dict[str, Any]:
        """
        Validate loaded data and return validation report
        验证加载的数据并返回验证报告
        
        Returns:
            Dictionary containing validation results
        """
        validation_report = {
            'gb_data_valid': False,
            'isic_data_valid': False,
            'gb_records': 0,
            'isic_records': 0,
            'gb_levels': [],
            'isic_levels': [],
            'issues': []
        }
        
        # Validate GB data
        if self.gb_data is not None:
            validation_report['gb_data_valid'] = True
            validation_report['gb_records'] = len(self.gb_data)
            
            if 'Level_Standard' in self.gb_data.columns:
                validation_report['gb_levels'] = self.gb_data['Level_Standard'].unique().tolist()
            elif 'Category_level' in self.gb_data.columns:
                validation_report['gb_levels'] = self.gb_data['Category_level'].unique().tolist()
        else:
            validation_report['issues'].append("GB/T 4754-2017 data not loaded")
        
        # Validate ISIC data
        if self.isic_data is not None:
            validation_report['isic_data_valid'] = True
            validation_report['isic_records'] = len(self.isic_data)
            
            if 'Level_Standard' in self.isic_data.columns:
                validation_report['isic_levels'] = self.isic_data['Level_Standard'].unique().tolist()
            elif 'Classification_level' in self.isic_data.columns:
                validation_report['isic_levels'] = self.isic_data['Classification_level'].unique().tolist()
        else:
            validation_report['issues'].append("ISIC Rev.5 data not loaded")
        
        return validation_report
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics of loaded data
        获取加载数据的摘要统计
        
        Returns:
            Dictionary containing data summary
        """
        summary = {}
        
        if self.gb_data is not None:
            gb_summary = {
                'total_records': len(self.gb_data),
                'levels': {},
                'sample_records': self.gb_data.head(3).to_dict('records')
            }
            
            # Count by level
            level_col = 'Level_Standard' if 'Level_Standard' in self.gb_data.columns else 'Category_level'
            if level_col in self.gb_data.columns:
                gb_summary['levels'] = self.gb_data[level_col].value_counts().to_dict()
            
            summary['gb_data'] = gb_summary
        
        if self.isic_data is not None:
            isic_summary = {
                'total_records': len(self.isic_data),
                'levels': {},
                'sample_records': self.isic_data.head(3).to_dict('records')
            }
            
            # Count by level
            level_col = 'Level_Standard' if 'Level_Standard' in self.isic_data.columns else 'Classification_level'
            if level_col in self.isic_data.columns:
                isic_summary['levels'] = self.isic_data[level_col].value_counts().to_dict()
            
            summary['isic_data'] = isic_summary
        
        return summary


class IndustryMatcher:
    """
    Industry Classification Matching Algorithm
    产业分类匹配算法

    This class implements keyword-based matching algorithms to identify
    industry-related classifications in both GB/T 4754-2017 and ISIC Rev.5 standards.
    """

    def __init__(self, data_loader: DataLoader):
        """
        Initialize IndustryMatcher with data loader

        Args:
            data_loader: DataLoader instance with loaded classification data
        """
        self.data_loader = data_loader
        self.keywords_lib = IndustryKeywords()

        # Ensure data is loaded
        if self.data_loader.gb_data is None:
            self.data_loader.load_gb_data()
        if self.data_loader.isic_data is None:
            self.data_loader.load_isic_data()

    def exact_match(self, text: str, keywords: List[str], case_sensitive: bool = False) -> Tuple[bool, List[str], float]:
        """
        Perform exact keyword matching in text
        在文本中执行精确关键词匹配

        Args:
            text: Text to search in
            keywords: List of keywords to match
            case_sensitive: Whether to perform case-sensitive matching

        Returns:
            Tuple of (is_match, matched_keywords, match_score)
        """
        if not text or not keywords:
            return False, [], 0.0

        search_text = text if case_sensitive else text.lower()
        matched_keywords = []

        for keyword in keywords:
            check_keyword = keyword if case_sensitive else keyword.lower()
            if check_keyword in search_text:
                matched_keywords.append(keyword)

        is_match = len(matched_keywords) > 0
        match_score = len(matched_keywords) / len(keywords) if keywords else 0.0

        return is_match, matched_keywords, match_score

    def fuzzy_match(self, text: str, keywords: List[str], threshold: float = 0.6) -> Tuple[bool, List[str], float]:
        """
        Perform fuzzy keyword matching with partial matching support
        执行支持部分匹配的模糊关键词匹配

        Args:
            text: Text to search in
            keywords: List of keywords to match
            threshold: Minimum similarity threshold for fuzzy matching

        Returns:
            Tuple of (is_match, matched_keywords, match_score)
        """
        if not text or not keywords:
            return False, [], 0.0

        search_text = text.lower()
        matched_keywords = []

        for keyword in keywords:
            check_keyword = keyword.lower()

            # Exact match
            if check_keyword in search_text:
                matched_keywords.append(keyword)
                continue

            # Partial match - check if any word in keyword appears in text
            keyword_words = check_keyword.split()
            text_words = search_text.split()

            word_matches = 0
            for kw_word in keyword_words:
                for text_word in text_words:
                    if kw_word in text_word or text_word in kw_word:
                        word_matches += 1
                        break

            # Calculate similarity based on word matches
            similarity = word_matches / len(keyword_words) if keyword_words else 0.0

            if similarity >= threshold:
                matched_keywords.append(keyword)

        is_match = len(matched_keywords) > 0
        match_score = len(matched_keywords) / len(keywords) if keywords else 0.0

        return is_match, matched_keywords, match_score

    def search_by_industry(self, industry: IndustryType,
                          classification_system: str = 'both',
                          match_type: str = 'fuzzy',
                          threshold: float = 0.3) -> Dict[str, List[Dict]]:
        """
        Search for classifications related to a specific industry
        搜索与特定产业相关的分类

        Args:
            industry: Industry type to search for
            classification_system: 'gb', 'isic', or 'both'
            match_type: 'exact' or 'fuzzy'
            threshold: Minimum match score threshold

        Returns:
            Dictionary containing matched classifications for each system
        """
        results = {}

        # Get industry keywords
        industry_keywords = self.keywords_lib.get_industry_keywords(industry, 'both')
        all_keywords = []
        for lang_keywords in industry_keywords.values():
            all_keywords.extend(list(lang_keywords))

        if not all_keywords:
            return results

        # Search in GB/T 4754-2017
        if classification_system in ['gb', 'both'] and self.data_loader.gb_data is not None:
            gb_matches = self._search_in_dataframe(
                self.data_loader.gb_data,
                all_keywords,
                'gb',
                match_type,
                threshold
            )
            results['gb'] = gb_matches

        # Search in ISIC Rev.5
        if classification_system in ['isic', 'both'] and self.data_loader.isic_data is not None:
            isic_matches = self._search_in_dataframe(
                self.data_loader.isic_data,
                all_keywords,
                'isic',
                match_type,
                threshold
            )
            results['isic'] = isic_matches

        return results

    def _search_in_dataframe(self, df: pd.DataFrame, keywords: List[str],
                           system: str, match_type: str, threshold: float) -> List[Dict]:
        """
        Search for keywords in a classification dataframe
        在分类数据框中搜索关键词

        Args:
            df: Classification dataframe
            keywords: List of keywords to search for
            system: 'gb' or 'isic'
            match_type: 'exact' or 'fuzzy'
            threshold: Minimum match score threshold

        Returns:
            List of matched classification records
        """
        matches = []

        # Determine column names based on system
        if system == 'gb':
            level_col = 'Level_Standard' if 'Level_Standard' in df.columns else 'Category_level'
            name_col = 'Name'
            code_col = 'Code'
            keyword_col = 'Keywords' if 'Keywords' in df.columns else 'Core_keyword'
        else:  # isic
            level_col = 'Level_Standard' if 'Level_Standard' in df.columns else 'Classification_level'
            name_col = 'Name'
            code_col = 'Code'
            keyword_col = 'Keywords' if 'Keywords' in df.columns else 'Core_keyword'

        for idx, row in df.iterrows():
            # Combine name and keywords for searching
            search_text = str(row.get(name_col, ''))
            if keyword_col in row and pd.notna(row[keyword_col]):
                search_text += ' ' + str(row[keyword_col])

            # Perform matching
            if match_type == 'exact':
                is_match, matched_keywords, match_score = self.exact_match(search_text, keywords)
            else:  # fuzzy
                is_match, matched_keywords, match_score = self.fuzzy_match(search_text, keywords)

            # Add to results if match score meets threshold
            if is_match and match_score >= threshold:
                match_record = {
                    'system': system,
                    'code': str(row.get(code_col, '')),
                    'name': str(row.get(name_col, '')),
                    'level': str(row.get(level_col, '')),
                    'matched_keywords': matched_keywords,
                    'match_score': match_score,
                    'search_text': search_text[:200] + '...' if len(search_text) > 200 else search_text
                }

                # Add parent code if available
                parent_col = 'Parent_code' if 'Parent_code' in df.columns else 'Parent_code_CN'
                if parent_col in row:
                    match_record['parent_code'] = str(row.get(parent_col, ''))

                matches.append(match_record)

        # Sort by match score (descending)
        matches.sort(key=lambda x: x['match_score'], reverse=True)

        return matches

    def get_hierarchy_matches(self, industry: IndustryType,
                            classification_system: str = 'both') -> Dict[str, Dict[str, List[Dict]]]:
        """
        Get industry matches organized by hierarchy levels
        获取按层级组织的产业匹配结果

        Args:
            industry: Industry type to search for
            classification_system: 'gb', 'isic', or 'both'

        Returns:
            Dictionary organized by system and hierarchy level
        """
        # Get all matches
        all_matches = self.search_by_industry(industry, classification_system, 'fuzzy', 0.2)

        hierarchy_results = {}

        for system, matches in all_matches.items():
            if system not in hierarchy_results:
                hierarchy_results[system] = {}

            # Define hierarchy levels for each system
            if system == 'gb':
                levels = ['Category', 'Major', 'Medium', 'Subclass']
            else:  # isic
                levels = ['Section', 'Division', 'Group', 'Class']

            # Organize matches by level
            for level in levels:
                level_matches = [m for m in matches if m['level'] == level]
                if level_matches:
                    hierarchy_results[system][level] = level_matches

        return hierarchy_results


# Example usage and testing
if __name__ == "__main__":
    # Test data loading and industry matching
    print("=== Testing Data Loading and Industry Matching ===")

    # Initialize data loader
    loader = DataLoader()

    try:
        # Load data
        gb_data = loader.load_gb_data()
        isic_data = loader.load_isic_data()
        print(f"Data loaded - GB: {gb_data.shape}, ISIC: {isic_data.shape}")

        # Initialize industry matcher
        matcher = IndustryMatcher(loader)
        print("Industry matcher initialized")

        # Test industry matching for each industry type
        industries = [IndustryType.AI, IndustryType.AUTOMOTIVE, IndustryType.BIOMEDICINE, IndustryType.CLEAN_TECH]

        for industry in industries:
            print(f"\n=== Testing {industry.value.upper()} Industry Matching ===")

            # Search for industry-related classifications
            matches = matcher.search_by_industry(industry, 'both', 'fuzzy', 0.3)

            for system, system_matches in matches.items():
                print(f"\n{system.upper()} System - Found {len(system_matches)} matches:")

                # Show top 3 matches
                for i, match in enumerate(system_matches[:3]):
                    print(f"  {i+1}. [{match['level']}] {match['code']} - {match['name']}")
                    print(f"     Score: {match['match_score']:.3f}, Keywords: {match['matched_keywords'][:3]}")

            # Get hierarchy matches
            hierarchy_matches = matcher.get_hierarchy_matches(industry, 'both')
            print(f"\nHierarchy analysis for {industry.value}:")
            for system, levels in hierarchy_matches.items():
                print(f"  {system.upper()}: {list(levels.keys())}")

        print("\n=== Industry Matching Test Completed Successfully ===")

    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
