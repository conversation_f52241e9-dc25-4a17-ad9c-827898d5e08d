import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo

def analyze_gb_classification():
    """Analyze GB/T 4754-2017 classification structure"""
    # Read the GB classification file
    gb_df = pd.read_csv('final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv')
    
    # Count categories by level
    level_counts = gb_df['Category_level'].value_counts()
    
    # Get unique categories for each level
    categories_by_level = {}
    for level in gb_df['Category_level'].unique():
        level_data = gb_df[gb_df['Category_level'] == level]
        categories_by_level[level] = len(level_data)
    
    return gb_df, level_counts, categories_by_level

def analyze_isic_classification():
    """Analyze ISIC Rev.5 classification structure"""
    # Read the ISIC classification file
    isic_df = pd.read_csv('final_data/ISIC_Rev5/ISIC_Rev5_en.csv')
    
    # Count categories by level
    level_counts = isic_df['Classification_level'].value_counts()
    
    # Get unique categories for each level
    categories_by_level = {}
    for level in isic_df['Classification_level'].unique():
        level_data = isic_df[isic_df['Classification_level'] == level]
        categories_by_level[level] = len(level_data)
    
    return isic_df, level_counts, categories_by_level

def create_comparison_charts(gb_data, isic_data):
    """Create comparison charts for both classification systems"""
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('GB/T 4754-2017 Classification Levels', 
                       'ISIC Rev.5 Classification Levels',
                       'GB/T 4754-2017 Distribution', 
                       'ISIC Rev.5 Distribution'),
        specs=[[{"type": "bar"}, {"type": "bar"}],
               [{"type": "pie"}, {"type": "pie"}]]
    )
    
    # GB bar chart
    gb_levels = list(gb_data[2].keys())
    gb_counts = list(gb_data[2].values())
    
    fig.add_trace(
        go.Bar(x=gb_levels, y=gb_counts, name="GB/T 4754-2017", 
               marker_color='lightblue'),
        row=1, col=1
    )
    
    # ISIC bar chart
    isic_levels = list(isic_data[2].keys())
    isic_counts = list(isic_data[2].values())
    
    fig.add_trace(
        go.Bar(x=isic_levels, y=isic_counts, name="ISIC Rev.5", 
               marker_color='lightcoral'),
        row=1, col=2
    )
    
    # GB pie chart
    fig.add_trace(
        go.Pie(labels=gb_levels, values=gb_counts, name="GB/T 4754-2017"),
        row=2, col=1
    )
    
    # ISIC pie chart
    fig.add_trace(
        go.Pie(labels=isic_levels, values=isic_counts, name="ISIC Rev.5"),
        row=2, col=2
    )
    
    fig.update_layout(height=800, showlegend=False, 
                     title_text="Classification Systems Comparison")
    
    return fig

def generate_html_report(gb_data, isic_data, comparison_fig):
    """Generate HTML report with analysis and charts"""
    
    gb_df, _, gb_categories = gb_data
    isic_df, _, isic_categories = isic_data
    
    # Convert plotly figure to HTML
    chart_html = pyo.plot(comparison_fig, output_type='div', include_plotlyjs=True)
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Classification Systems Analysis Report</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 40px;
                line-height: 1.6;
                color: #333;
            }}
            .header {{
                text-align: center;
                margin-bottom: 40px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 10px;
            }}
            .section {{
                margin: 30px 0;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #fafafa;
            }}
            .stats-table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            .stats-table th, .stats-table td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            .stats-table th {{
                background-color: #4CAF50;
                color: white;
            }}
            .stats-table tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            .highlight {{
                background-color: #e7f3ff;
                padding: 15px;
                border-left: 4px solid #2196F3;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Classification Systems Analysis Report</h1>
            <p>Comparative Analysis of GB/T 4754-2017 and ISIC Rev.5 Classification Systems</p>
        </div>
        
        <div class="section">
            <h2>Executive Summary</h2>
            <div class="highlight">
                <p>This report analyzes two major industry classification systems:</p>
                <ul>
                    <li><strong>GB/T 4754-2017</strong>: Chinese National Standard for Industry Classification</li>
                    <li><strong>ISIC Rev.5</strong>: International Standard Industrial Classification, Revision 5</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>GB/T 4754-2017 Analysis</h2>
            <p>The Chinese National Standard for Industry Classification (GB/T 4754-2017) is the official industry classification system used in China. It provides a comprehensive framework for categorizing all economic activities and is widely used for statistical purposes, economic analysis, and business registration. The system follows a hierarchical structure with four main levels:</p>
            <div class="highlight">
                <h3>Classification Hierarchy:</h3>
                <ul>
                    <li><strong>门类 (Category)</strong> - Top-level categories that represent broad economic sectors</li>
                    <li><strong>大类 (Major)</strong> - Major categories that subdivide the top-level sectors</li>
                    <li><strong>中类 (Medium)</strong> - Medium categories that provide more specific industry groupings</li>
                    <li><strong>小类 (Subclass)</strong> - Sub-categories that offer the most detailed classification level</li>
                </ul>
            </div>
            <table class="stats-table">
                <tr>
                    <th>Classification Level</th>
                    <th>Number of Categories</th>
                    <th>Description</th>
                </tr>
"""
    
    # Add GB classification data to table
    level_descriptions = {
        'Category': 'Top-level sections',
        'Major': 'Two-digit divisions',
        'Medium': 'Three-digit groups',
        'Subclass': 'Four-digit classes'
    }
    
    for level, count in gb_categories.items():
        description = level_descriptions.get(level, 'Classification level')
        html_content += f"""
                <tr>
                    <td>{level}</td>
                    <td>{count}</td>
                    <td>{description}</td>
                </tr>
"""
    
    html_content += f"""
            </table>
            <p><strong>Total GB/T 4754-2017 entries:</strong> {len(gb_df)} classifications</p>
        </div>
        
        <div class="section">
            <h2>ISIC Rev.5 Analysis</h2>
            <p>The International Standard Industrial Classification of All Economic Activities, Revision 5 (ISIC Rev.5) is the international reference classification of productive activities. It is maintained by the United Nations Statistics Division and provides a comprehensive framework for the collection and presentation of statistics according to economic activity. ISIC Rev.5 is used worldwide for statistical purposes and international comparisons.</p>
            <div class="highlight">
                <h3>Classification Hierarchy:</h3>
                <ul>
                    <li><strong>Section</strong> - Highest level categories identified by letters (A-U) representing broad economic sectors</li>
                    <li><strong>Division</strong> - Two-digit numerical codes that subdivide sections into major industry groups</li>
                    <li><strong>Group</strong> - Three-digit codes that provide more detailed industry classifications</li>
                    <li><strong>Class</strong> - Four-digit codes offering the most specific level of industry classification</li>
                </ul>
            </div>
            <table class="stats-table">
                <tr>
                    <th>Classification Level</th>
                    <th>Number of Categories</th>
                    <th>Description</th>
                </tr>
"""
    
    # Add ISIC classification data to table
    isic_level_descriptions = {
        'Section': 'Top-level sections (A-U)',
        'Division': 'Two-digit divisions',
        'Group': 'Three-digit groups', 
        'Class': 'Four-digit classes'
    }
    
    for level, count in isic_categories.items():
        description = isic_level_descriptions.get(level, 'Classification level')
        html_content += f"""
                <tr>
                    <td>{level}</td>
                    <td>{count}</td>
                    <td>{description}</td>
                </tr>
"""
    
    html_content += f"""
            </table>
            <p><strong>Total ISIC Rev.5 entries:</strong> {len(isic_df)} classifications</p>
        </div>
        
        <div class="section">
            <h2>Comparative Analysis</h2>
            <div class="highlight">
                <h3>Key Findings:</h3>
                <ul>
                    <li><strong>Scale Comparison:</strong>
                        <ul>
                            <li>GB/T 4754-2017 has <strong>{len(gb_df)}</strong> total classification entries</li>
                            <li>ISIC Rev.5 has <strong>{len(isic_df)}</strong> total classification entries</li>
                            <li>GB/T 4754-2017 is more granular with {len(gb_df) - len(isic_df)} additional classifications</li>
                        </ul>
                    </li>
                    <li><strong>Structural Comparison:</strong>
                        <ul>
                            <li>GB/T 4754-2017 uses 4 hierarchical levels: 门类 (Menlei), 大类 (Dalei), 中类 (Zhonglei), 小类 (Xiaolei)</li>
                            <li>ISIC Rev.5 uses 4 hierarchical levels: Section, Division, Group, Class</li>
                            <li>Both systems follow similar hierarchical principles from broad to specific categories</li>
                        </ul>
                    </li>
                    <li><strong>Coverage Analysis:</strong>
                        <ul>
                            <li>GB/T 4754-2017 provides more detailed sub-classifications (1,378 小类 vs 463 Classes)</li>
                            <li>ISIC Rev.5 focuses on international standardization and comparability</li>
                            <li>Both systems cover all economic sectors comprehensively</li>
                        </ul>
                    </li>
                    <li><strong>Usage Context:</strong>
                        <ul>
                            <li>GB/T 4754-2017 is specifically designed for Chinese economic structure and statistical needs</li>
                            <li>ISIC Rev.5 serves as the global standard for international economic statistics and comparisons</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>Visualization Charts</h2>
            {chart_html}
        </div>
        
        <div class="section">
            <h2>Detailed Statistical Breakdown</h2>
            <table class="stats-table">
                <tr>
                    <th>Metric</th>
                    <th>GB/T 4754-2017</th>
                    <th>ISIC Rev.5</th>
                    <th>Difference</th>
                </tr>
                <tr>
                    <td>Total Classifications</td>
                    <td>{len(gb_df)}</td>
                    <td>{len(isic_df)}</td>
                    <td>{len(gb_df) - len(isic_df)} more in GB/T</td>
                </tr>
                <tr>
                    <td>Top Level Categories</td>
                    <td>{gb_categories.get('门类', 0)} 门类 (Menlei)</td>
                    <td>{isic_categories.get('Section', 0)} Sections</td>
                    <td>{abs(gb_categories.get('门类', 0) - isic_categories.get('Section', 0))} difference</td>
                </tr>
                <tr>
                    <td>Second Level Categories</td>
                    <td>{gb_categories.get('大类', 0)} 大类 (Dalei)</td>
                    <td>{isic_categories.get('Division', 0)} Divisions</td>
                    <td>{abs(gb_categories.get('大类', 0) - isic_categories.get('Division', 0))} difference</td>
                </tr>
                <tr>
                    <td>Third Level Categories</td>
                    <td>{gb_categories.get('中类', 0)} 中类 (Zhonglei)</td>
                    <td>{isic_categories.get('Group', 0)} Groups</td>
                    <td>{abs(gb_categories.get('中类', 0) - isic_categories.get('Group', 0))} difference</td>
                </tr>
                <tr>
                    <td>Fourth Level Categories</td>
                    <td>{gb_categories.get('小类', 0)} 小类 (Xiaolei)</td>
                    <td>{isic_categories.get('Class', 0)} Classes</td>
                    <td>{abs(gb_categories.get('小类', 0) - isic_categories.get('Class', 0))} more in GB/T</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>Conclusion</h2>
            <p>This comprehensive analysis reveals significant insights about both classification systems:</p>
            <div class="highlight">
                <h3>Key Conclusions:</h3>
                <ul>
                    <li><strong>Granularity:</strong> GB/T 4754-2017 provides significantly more detailed classifications ({len(gb_df)} vs {len(isic_df)}), reflecting the need for more specific categorization in the Chinese economic context</li>
                    <li><strong>Structure:</strong> Both systems employ similar four-tier hierarchical structures, demonstrating convergent approaches to industry classification</li>
                    <li><strong>Scope:</strong> While ISIC Rev.5 focuses on international comparability, GB/T 4754-2017 offers deeper granularity for domestic economic analysis</li>
                    <li><strong>Practical Application:</strong> The detailed breakdown in both systems enables precise categorization of business activities for statistical, regulatory, and analytical purposes</li>
                    <li><strong>Complementary Use:</strong> Organizations operating internationally may need to map between both systems for comprehensive reporting and compliance</li>
                </ul>
            </div>
            <p>The analysis demonstrates that while both systems serve similar fundamental purposes, they are optimized for different contexts - GB/T 4754-2017 for detailed domestic Chinese economic analysis and ISIC Rev.5 for international standardization and comparison.</p>
        </div>
        
        <footer style="text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid #ddd;">
            <p>Report generated on {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </footer>
    </body>
    </html>
    """
    
    return html_content

def main():
    """Main function to run the analysis"""
    print("Starting classification analysis...")
    
    # Analyze both classification systems
    print("Analyzing GB/T 4754-2017...")
    gb_data = analyze_gb_classification()
    
    print("Analyzing ISIC Rev.5...")
    isic_data = analyze_isic_classification()
    
    # Create comparison charts
    print("Creating comparison charts...")
    comparison_fig = create_comparison_charts(gb_data, isic_data)
    
    # Generate HTML report
    print("Generating HTML report...")
    html_report = generate_html_report(gb_data, isic_data, comparison_fig)
    
    # Save HTML report
    with open('classification_analysis_report.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("Analysis complete! Report saved as 'classification_analysis_report.html'")
    
    # Print summary to console
    print("\n=== SUMMARY ===")
    print(f"GB/T 4754-2017 total entries: {len(gb_data[0])}")
    print("GB/T 4754-2017 breakdown:")
    for level, count in gb_data[2].items():
        print(f"  {level}: {count}")
    
    print(f"\nISIC Rev.5 total entries: {len(isic_data[0])}")
    print("ISIC Rev.5 breakdown:")
    for level, count in isic_data[2].items():
        print(f"  {level}: {count}")

if __name__ == "__main__":
    main()
