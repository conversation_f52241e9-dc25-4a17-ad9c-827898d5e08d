#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Industry Keywords Library - 产业关键词库

This module defines comprehensive keyword sets for four key industries:
- AI Artificial Intelligence (AI人工智能)
- Automotive (自动驾驶/新能源汽车)
- Biomedicine (生物医药)
- Clean Technology (清洁技术)

Author: AI Assistant
Created: January 2025
Version: 1.0
"""

import re
from typing import Dict, List, Set, Tuple, Optional
from enum import Enum


class IndustryType(Enum):
    """Industry type enumeration"""
    AI = "ai"
    AUTOMOTIVE = "automotive"
    BIOMEDICINE = "biomedicine"
    CLEAN_TECH = "clean_tech"


class IndustryKeywords:
    """
    Industry Keywords Library
    
    Provides comprehensive keyword sets for industry classification matching
    across GB/T 4754-2017 and ISIC Rev.5 standards.
    """
    
    def __init__(self):
        """Initialize the keyword library with comprehensive industry keywords"""
        self._initialize_keywords()
    
    def _initialize_keywords(self):
        """Initialize all industry keyword sets"""
        
        # AI人工智能产业关键词
        self.AI_KEYWORDS = {
            'chinese': {
                # 核心AI技术
                '人工智能', 'AI', '机器学习', '深度学习', '神经网络', '算法',
                '智能系统', '智能化', '自动化', '智能控制', '智能识别',
                
                # AI应用领域
                '计算机视觉', '自然语言处理', '语音识别', '图像识别', '模式识别',
                '智能推荐', '智能分析', '智能决策', '智能优化',
                
                # AI相关技术
                '大数据', '云计算', '边缘计算', '物联网', 'IoT', '区块链',
                '数据挖掘', '数据分析', '预测分析', '机器人', '无人机',
                
                # AI软件和平台
                '智能软件', '智能平台', '算法平台', 'AI芯片', '智能芯片',
                '神经网络芯片', '深度学习框架', '机器学习平台'
            },
            'english': {
                'artificial intelligence', 'AI', 'machine learning', 'deep learning',
                'neural network', 'algorithm', 'intelligent system', 'automation',
                'smart system', 'cognitive computing', 'computer vision',
                'natural language processing', 'NLP', 'speech recognition',
                'image recognition', 'pattern recognition', 'data mining',
                'big data', 'cloud computing', 'edge computing', 'IoT',
                'blockchain', 'robotics', 'drone', 'UAV', 'intelligent software',
                'AI platform', 'AI chip', 'neural processing unit', 'NPU'
            }
        }
        
        # 自动驾驶/新能源汽车产业关键词
        self.AUTOMOTIVE_KEYWORDS = {
            'chinese': {
                # 新能源汽车
                '新能源汽车', '电动汽车', '混合动力', '燃料电池', '纯电动',
                '插电式混合动力', '增程式', '电池', '锂电池', '动力电池',
                '充电桩', '充电站', '换电站', '电机', '电控',
                
                # 自动驾驶
                '自动驾驶', '无人驾驶', '智能驾驶', '辅助驾驶', 'ADAS',
                '车联网', '智能网联汽车', '车路协同', '激光雷达', '毫米波雷达',
                '摄像头', '传感器', '高精地图', '定位系统', 'GPS', '北斗',
                
                # 汽车制造
                '汽车制造', '整车制造', '汽车零部件', '发动机', '变速器',
                '底盘', '车身', '内饰', '汽车电子', '车载系统',
                
                # 智能汽车技术
                '智能座舱', '车机系统', '车载娱乐', '语音交互', '手势控制',
                '人机交互', '车载通信', '5G车联网', 'V2X', 'OTA升级'
            },
            'english': {
                'new energy vehicle', 'NEV', 'electric vehicle', 'EV', 'hybrid',
                'fuel cell', 'battery electric vehicle', 'BEV', 'plug-in hybrid',
                'PHEV', 'lithium battery', 'power battery', 'charging station',
                'charging pile', 'electric motor', 'motor control',
                'autonomous driving', 'self-driving', 'automated driving', 'ADAS',
                'connected vehicle', 'intelligent connected vehicle', 'ICV',
                'vehicle-to-everything', 'V2X', 'lidar', 'radar', 'camera',
                'sensor', 'high-definition map', 'GPS', 'automotive manufacturing',
                'vehicle manufacturing', 'auto parts', 'engine', 'transmission',
                'chassis', 'automotive electronics', 'infotainment system',
                'smart cockpit', 'over-the-air', 'OTA'
            }
        }
        
        # 生物医药产业关键词
        self.BIOMEDICINE_KEYWORDS = {
            'chinese': {
                # 生物技术
                '生物技术', '生物制药', '基因工程', '细胞工程', '蛋白质工程',
                '生物制品', '疫苗', '抗体', '单克隆抗体', '重组蛋白',
                '基因治疗', '细胞治疗', '免疫治疗', '干细胞', '组织工程',
                
                # 医药制造
                '医药制造', '药物研发', '新药开发', '临床试验', '药物生产',
                '化学药', '中药', '生物药', '原料药', '制剂',
                '药品', '医药', '制药', '药厂', '医药企业',
                
                # 医疗器械
                '医疗器械', '医疗设备', '诊断设备', '治疗设备', '手术器械',
                '体外诊断', 'IVD', '医学影像', 'CT', 'MRI', '超声', 'X光',
                '心电图', '血压计', '血糖仪', '呼吸机', '监护仪',
                
                # 精准医疗
                '精准医疗', '个性化医疗', '基因检测', '分子诊断', '生物标志物',
                '靶向治疗', '伴随诊断', '液体活检', '测序', '基因组学',
                '蛋白组学', '代谢组学', '生物信息学'
            },
            'english': {
                'biotechnology', 'biopharmaceutical', 'genetic engineering',
                'cell engineering', 'protein engineering', 'biologics',
                'vaccine', 'antibody', 'monoclonal antibody', 'recombinant protein',
                'gene therapy', 'cell therapy', 'immunotherapy', 'stem cell',
                'tissue engineering', 'pharmaceutical manufacturing',
                'drug development', 'clinical trial', 'chemical drug',
                'traditional medicine', 'biological drug', 'API',
                'pharmaceutical', 'medicine', 'medical device',
                'diagnostic equipment', 'therapeutic equipment', 'surgical instrument',
                'in vitro diagnostics', 'IVD', 'medical imaging', 'CT', 'MRI',
                'ultrasound', 'X-ray', 'ECG', 'blood pressure monitor',
                'glucose meter', 'ventilator', 'patient monitor',
                'precision medicine', 'personalized medicine', 'genetic testing',
                'molecular diagnostics', 'biomarker', 'targeted therapy',
                'companion diagnostics', 'liquid biopsy', 'sequencing',
                'genomics', 'proteomics', 'metabolomics', 'bioinformatics'
            }
        }
        
        # 清洁技术产业关键词
        self.CLEAN_TECH_KEYWORDS = {
            'chinese': {
                # 太阳能/光伏
                '太阳能', '光伏', '太阳能电池', '光伏电池', '硅片', '电池片',
                '组件', '逆变器', '光伏系统', '分布式光伏', '集中式光伏',
                '光伏发电', '太阳能发电', '光伏组件', '光伏逆变器',
                
                # 风能
                '风能', '风力发电', '风电', '风机', '风力发电机', '海上风电',
                '陆上风电', '风电场', '风电设备', '叶片', '齿轮箱', '发电机',
                
                # 其他清洁能源
                '清洁能源', '可再生能源', '新能源', '绿色能源', '水力发电',
                '核能', '核电', '地热能', '生物质能', '氢能', '燃料电池',
                
                # 储能技术
                '储能', '电池储能', '抽水蓄能', '压缩空气储能', '飞轮储能',
                '储能系统', '电网储能', '用户侧储能', '电化学储能',
                
                # 节能环保
                '节能', '环保', '节能减排', '碳减排', '碳中和', '碳达峰',
                '绿色制造', '循环经济', '清洁生产', '环境治理', '污水处理',
                '垃圾处理', '废气处理', '土壤修复', '环境监测'
            },
            'english': {
                'solar energy', 'photovoltaic', 'PV', 'solar cell', 'solar panel',
                'silicon wafer', 'solar module', 'inverter', 'PV system',
                'distributed PV', 'utility-scale PV', 'solar power generation',
                'wind energy', 'wind power', 'wind turbine', 'offshore wind',
                'onshore wind', 'wind farm', 'wind equipment', 'blade',
                'gearbox', 'generator', 'clean energy', 'renewable energy',
                'green energy', 'hydropower', 'nuclear power', 'geothermal',
                'biomass energy', 'hydrogen energy', 'fuel cell',
                'energy storage', 'battery storage', 'pumped hydro storage',
                'compressed air energy storage', 'flywheel storage',
                'grid storage', 'electrochemical storage', 'energy efficiency',
                'environmental protection', 'emission reduction', 'carbon neutral',
                'carbon peak', 'green manufacturing', 'circular economy',
                'clean production', 'environmental treatment', 'wastewater treatment',
                'waste treatment', 'exhaust gas treatment', 'soil remediation',
                'environmental monitoring'
            }
        }
    
    def get_industry_keywords(self, industry: IndustryType, language: str = 'both') -> Dict[str, Set[str]]:
        """
        Get keywords for a specific industry
        
        Args:
            industry: Industry type
            language: 'chinese', 'english', or 'both'
            
        Returns:
            Dictionary containing keyword sets
        """
        industry_map = {
            IndustryType.AI: self.AI_KEYWORDS,
            IndustryType.AUTOMOTIVE: self.AUTOMOTIVE_KEYWORDS,
            IndustryType.BIOMEDICINE: self.BIOMEDICINE_KEYWORDS,
            IndustryType.CLEAN_TECH: self.CLEAN_TECH_KEYWORDS
        }
        
        keywords = industry_map.get(industry, {})
        
        if language == 'both':
            return keywords
        elif language in keywords:
            return {language: keywords[language]}
        else:
            return {}
    
    def match_keywords(self, text: str, industry: IndustryType, 
                      match_type: str = 'any', case_sensitive: bool = False) -> Tuple[bool, List[str], float]:
        """
        Match keywords in text for a specific industry
        
        Args:
            text: Text to search in
            industry: Industry type to match against
            match_type: 'any' (any keyword match) or 'all' (all keywords must match)
            case_sensitive: Whether to perform case-sensitive matching
            
        Returns:
            Tuple of (is_match, matched_keywords, match_score)
        """
        if not text:
            return False, [], 0.0
        
        # Get keywords for the industry
        keywords = self.get_industry_keywords(industry, 'both')
        all_keywords = set()
        
        for lang_keywords in keywords.values():
            all_keywords.update(lang_keywords)
        
        if not all_keywords:
            return False, [], 0.0
        
        # Prepare text for matching
        search_text = text if case_sensitive else text.lower()
        matched_keywords = []
        
        # Check each keyword
        for keyword in all_keywords:
            check_keyword = keyword if case_sensitive else keyword.lower()
            if check_keyword in search_text:
                matched_keywords.append(keyword)
        
        # Calculate match results
        is_match = False
        if match_type == 'any':
            is_match = len(matched_keywords) > 0
        elif match_type == 'all':
            is_match = len(matched_keywords) == len(all_keywords)
        
        # Calculate match score (percentage of keywords matched)
        match_score = len(matched_keywords) / len(all_keywords) if all_keywords else 0.0
        
        return is_match, matched_keywords, match_score
    
    def get_all_industries(self) -> List[IndustryType]:
        """Get list of all available industries"""
        return list(IndustryType)
    
    def get_industry_summary(self) -> Dict[str, Dict[str, int]]:
        """Get summary statistics for all industries"""
        summary = {}
        
        for industry in IndustryType:
            keywords = self.get_industry_keywords(industry, 'both')
            summary[industry.value] = {
                'chinese_keywords': len(keywords.get('chinese', set())),
                'english_keywords': len(keywords.get('english', set())),
                'total_keywords': len(keywords.get('chinese', set())) + len(keywords.get('english', set()))
            }
        
        return summary


# Example usage and testing
if __name__ == "__main__":
    # Initialize keyword library
    keywords_lib = IndustryKeywords()
    
    # Print summary
    print("=== Industry Keywords Library Summary ===")
    summary = keywords_lib.get_industry_summary()
    for industry, stats in summary.items():
        print(f"{industry.upper()}: {stats['total_keywords']} total keywords "
              f"({stats['chinese_keywords']} Chinese, {stats['english_keywords']} English)")
    
    # Test keyword matching
    print("\n=== Keyword Matching Tests ===")
    test_cases = [
        ("人工智能软件开发", IndustryType.AI),
        ("新能源汽车制造", IndustryType.AUTOMOTIVE),
        ("生物制药研发", IndustryType.BIOMEDICINE),
        ("太阳能发电设备", IndustryType.CLEAN_TECH)
    ]
    
    for text, industry in test_cases:
        is_match, matched, score = keywords_lib.match_keywords(text, industry)
        print(f"Text: '{text}' -> Industry: {industry.value}")
        print(f"  Match: {is_match}, Score: {score:.2f}, Keywords: {matched[:3]}...")
        print()
